import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:ui_controls_library/widgets/utils/font_manager.dart';

/// Enum for distance unit types
enum DistanceUnit {
  // Metric units
  millimeter,
  centimeter,
  meter,
  kilometer,

  // Imperial units
  inch,
  foot,
  yard,
  mile,

  // Nautical
  nauticalMile,
}

/// Extension to provide unit symbols and conversion factors
extension DistanceUnitExtension on DistanceUnit {
  String get symbol {
    switch (this) {
      case DistanceUnit.millimeter:
        return 'mm';
      case DistanceUnit.centimeter:
        return 'cm';
      case DistanceUnit.meter:
        return 'm';
      case DistanceUnit.kilometer:
        return 'km';
      case DistanceUnit.inch:
        return 'in';
      case DistanceUnit.foot:
        return 'ft';
      case DistanceUnit.yard:
        return 'yd';
      case DistanceUnit.mile:
        return 'mi';
      case DistanceUnit.nauticalMile:
        return 'nmi';
    }
  }

  String get fullName {
    switch (this) {
      case DistanceUnit.millimeter:
        return 'Millimeter';
      case DistanceUnit.centimeter:
        return 'Centimeter';
      case DistanceUnit.meter:
        return 'Meter';
      case DistanceUnit.kilometer:
        return 'Kilometer';
      case DistanceUnit.inch:
        return 'Inch';
      case DistanceUnit.foot:
        return 'Foot';
      case DistanceUnit.yard:
        return 'Yard';
      case DistanceUnit.mile:
        return 'Mile';
      case DistanceUnit.nauticalMile:
        return 'Nautical Mile';
    }
  }

  /// Conversion factor to meters (base unit)
  double get toMeters {
    switch (this) {
      case DistanceUnit.millimeter:
        return 0.001;
      case DistanceUnit.centimeter:
        return 0.01;
      case DistanceUnit.meter:
        return 1.0;
      case DistanceUnit.kilometer:
        return 1000.0;
      case DistanceUnit.inch:
        return 0.0254;
      case DistanceUnit.foot:
        return 0.3048;
      case DistanceUnit.yard:
        return 0.9144;
      case DistanceUnit.mile:
        return 1609.344;
      case DistanceUnit.nauticalMile:
        return 1852.0;
    }
  }

  bool get isMetric {
    return this == DistanceUnit.millimeter ||
        this == DistanceUnit.centimeter ||
        this == DistanceUnit.meter ||
        this == DistanceUnit.kilometer;
  }

  bool get isImperial {
    return this == DistanceUnit.inch ||
        this == DistanceUnit.foot ||
        this == DistanceUnit.yard ||
        this == DistanceUnit.mile;
  }
}

/// A widget for inputting and displaying distance values with unit conversion.
///
/// This widget provides extensive customization options for displaying
/// and inputting distance values with various units and styles.
class DistanceWidget extends StatefulWidget {
  /// The initial distance value
  final double initialValue;

  /// The initial unit for the distance
  final DistanceUnit initialUnit;

  /// The minimum allowed value
  final double? minValue;

  /// The maximum allowed value
  final double? maxValue;

  /// The number of decimal places to display
  final int decimalPlaces;

  /// Whether to allow negative values
  final bool allowNegative;

  /// Whether to show unit selection dropdown
  final bool showUnitSelector;

  /// List of available units to choose from (if null, all units are available)
  final List<DistanceUnit>? availableUnits;

  /// Whether to show unit conversion option
  final bool showConversion;

  /// The target unit for conversion display (if null, no conversion is shown)
  final DistanceUnit? conversionUnit;

  /// Whether to show increment/decrement buttons
  final bool showButtons;

  /// The hover color for the border
  final Color? hoverColor;

  /// The focus color for the border
  final Color? focusColor;

  /// The step value for increment and decrement
  final double stepValue;

  /// Whether to show a clear button
  final bool showClearButton;

  /// Whether the widget is read-only
  final bool readOnly;

  /// Whether the widget is disabled
  final bool disabled;

  /// The label text for the input field
  final String? label;

  /// The hint text for the input field
  final String? hint;

  /// The helper text to display below the input field
  final String? helperText;

  /// The error text to display (if any)
  final String? errorText;

  /// Whether to show the unit on the left side of the input
  final bool unitOnLeft;

  /// Whether to use thousands separator
  final bool useThousandsSeparator;

  /// The text color
  final Color textColor;

  /// The background color
  final Color backgroundColor;

  /// The border color
  final Color borderColor;

  /// The focused border color
  final Color focusedBorderColor;

  /// The error border color
  final Color errorBorderColor;

  /// The border width
  final double borderWidth;

  /// The focused border width
  final double focusedBorderWidth;

  /// The error border width
  final double errorBorderWidth;

  /// The border radius
  final double borderRadius;

  /// Whether to show a border
  final bool hasBorder;

  /// The font size
  final double fontSize;

  /// The font weight
  final FontWeight fontWeight;

  /// The font family
  final String? fontFamily;

  /// Whether the widget is compact
  final bool isCompact;

  /// Whether to show a shadow
  final bool hasShadow;

  /// The elevation of the shadow
  final double elevation;

  /// The shadow color
  final Color? shadowColor;

  /// Whether to use dark theme
  final bool isDarkTheme;

  /// The text alignment
  final TextAlign textAlign;

  /// The label color
  final Color labelColor;

  /// The hint color
  final Color hintColor;

  /// The helper text color
  final Color helperTextColor;

  /// The error text color
  final Color errorTextColor;

  /// The prefix icon
  final IconData? prefixIcon;

  /// The suffix icon
  final IconData? suffixIcon;

  /// The prefix icon color
  final Color? prefixIconColor;

  /// The suffix icon color
  final Color? suffixIconColor;

  /// The width of the widget
  final double? width;

  /// The height of the widget
  final double? height;

  /// The padding of the widget
  final EdgeInsetsGeometry padding;

  /// The margin of the widget
  final EdgeInsetsGeometry margin;

  /// The cursor color
  final Color? cursorColor;

  /// The cursor width
  final double cursorWidth;

  /// The cursor height
  final double? cursorHeight;

  /// The cursor radius
  final Radius? cursorRadius;

  /// Callback when the value changes
  final Function(double value, DistanceUnit unit)? onChanged;

  /// Callback when the value is submitted
  final Function(double value, DistanceUnit unit)? onSubmitted;

  /// Callback when the unit changes
  final Function(DistanceUnit unit)? onUnitChanged;

  /// Callback when the clear button is pressed
  final VoidCallback? onClear;

  /// Callback when the input field is tapped
  final VoidCallback? onTap;

  /// Whether to show the conversion result
  final bool showConversionResult;

  /// The color of the conversion result text
  final Color conversionResultColor;

  /// The font size of the conversion result
  final double conversionResultFontSize;

  /// The button color for increment/decrement buttons
  final Color? buttonColor;

  /// The button icon color for increment/decrement buttons
  final Color? buttonIconColor;

  /// The icon for the increment button
  final IconData incrementIcon;

  /// The icon for the decrement button
  final IconData decrementIcon;

  /// The size of the increment/decrement buttons
  final double buttonSize;

  /// The dropdown button color
  final Color? dropdownButtonColor;

  /// The dropdown text color
  final Color? dropdownTextColor;

  /// The dropdown icon color
  final Color? dropdownIconColor;

  /// The dropdown background color
  final Color? dropdownBackgroundColor;

  /// The dropdown border color
  final Color? dropdownBorderColor;

  /// The dropdown border radius
  final double dropdownBorderRadius;

  /// Whether to show a label for the conversion result
  final bool showConversionLabel;

  /// The label for the conversion result
  final String conversionLabel;

  /// Creates a distance widget.
  const DistanceWidget({
    super.key,
    this.initialValue = 0.0,
    this.initialUnit = DistanceUnit.meter,
    this.minValue,
    this.maxValue,
    this.decimalPlaces = 2,
    this.allowNegative = true,
    this.showUnitSelector = true,
    this.availableUnits,
    this.showConversion = false,
    this.conversionUnit,
    this.showButtons = true,
    this.hoverColor,
    this.focusColor,
    this.stepValue = 1.0,
    this.showClearButton = true,
    this.readOnly = false,
    this.disabled = false,
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.unitOnLeft = false,
    this.useThousandsSeparator = true,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.borderColor = const Color(0xFFCCCCCC),
    this.focusedBorderColor = Colors.blue,
    this.errorBorderColor = Colors.red,
    this.borderWidth = 1.0,
    this.focusedBorderWidth = 2.0,
    this.errorBorderWidth = 1.0,
    this.borderRadius = 4.0,
    this.hasBorder = true,
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.normal,
    this.fontFamily,
    this.isCompact = false,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.shadowColor,
    this.isDarkTheme = false,
    this.textAlign = TextAlign.start,
    this.labelColor = Colors.black87,
    this.hintColor = Colors.black54,
    this.helperTextColor = Colors.black54,
    this.errorTextColor = Colors.red,
    this.prefixIcon,
    this.suffixIcon,
    this.prefixIconColor,
    this.suffixIconColor,
    this.width,
    this.height,
    this.padding = const EdgeInsets.symmetric(horizontal: 12.0, vertical: 12.0),
    this.margin = EdgeInsets.zero,
    this.cursorColor,
    this.cursorWidth = 2.0,
    this.cursorHeight,
    this.cursorRadius,
    this.onChanged,
    this.onSubmitted,
    this.onUnitChanged,
    this.onClear,
    this.onTap,
    this.showConversionResult = true,
    this.conversionResultColor = Colors.grey,
    this.conversionResultFontSize = 14.0,
    this.buttonColor,
    this.buttonIconColor,
    this.incrementIcon = Icons.add,
    this.decrementIcon = Icons.remove,
    this.buttonSize = 36.0,
    this.dropdownButtonColor,
    this.dropdownTextColor,
    this.dropdownIconColor,
    this.dropdownBackgroundColor,
    this.dropdownBorderColor,
    this.dropdownBorderRadius = 4.0,
    this.showConversionLabel = true,
    this.conversionLabel = 'Equals:',
  });

  @override
  State<DistanceWidget> createState() => _DistanceWidgetState();
}

class _DistanceWidgetState extends State<DistanceWidget> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  late double _value;
  late DistanceUnit _unit;
  bool _hasError = false;
  String? _errorText;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _value = widget.initialValue;
    _unit = widget.initialUnit;
    _controller = TextEditingController(text: _formatValue(_value));
    _focusNode = FocusNode();

    _focusNode.addListener(() {
      if (!_focusNode.hasFocus) {
        _validateAndFormat();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  // Format the value according to the settings
  String _formatValue(double value) {
    if (value == 0 && _controller.text.isEmpty) {
      return '';
    }

    String formatted;
    if (widget.useThousandsSeparator) {
      // Format with thousands separator
      final formatter =
          NumberFormat.decimalPattern()
            ..minimumFractionDigits = widget.decimalPlaces
            ..maximumFractionDigits = widget.decimalPlaces;
      formatted = formatter.format(value);
    } else {
      // Format without thousands separator
      formatted = value.toStringAsFixed(widget.decimalPlaces);
    }

    return formatted;
  }

  // Parse the input text to a double value
  double _parseValue(String text) {
    if (text.isEmpty) {
      return 0.0;
    }

    // Remove thousands separators if used
    String cleanText = text;
    if (widget.useThousandsSeparator) {
      cleanText = text.replaceAll(RegExp(r'[^\d.-]'), '');
    }

    return double.tryParse(cleanText) ?? 0.0;
  }

  // Validate the input and format it
  void _validateAndFormat() {
    final inputValue = _parseValue(_controller.text);
    bool isValid = true;
    String? errorMessage;

    // Check min/max constraints
    if (widget.minValue != null && inputValue < widget.minValue!) {
      isValid = false;
      errorMessage = 'Value must be at least ${widget.minValue}';
    } else if (widget.maxValue != null && inputValue > widget.maxValue!) {
      isValid = false;
      errorMessage = 'Value must be at most ${widget.maxValue}';
    }

    // Check if negative values are allowed
    if (!widget.allowNegative && inputValue < 0) {
      isValid = false;
      errorMessage = 'Negative values are not allowed';
    }

    setState(() {
      _hasError = !isValid;
      _errorText = errorMessage;

      if (isValid) {
        _value = inputValue;
        _controller.text = _formatValue(_value);
        _controller.selection = TextSelection.fromPosition(
          TextPosition(offset: _controller.text.length),
        );
      }
    });
  }

  // Convert value from current unit to target unit
  double _convertValue(
    double value,
    DistanceUnit fromUnit,
    DistanceUnit toUnit,
  ) {
    // Convert to meters first (base unit)
    final valueInMeters = value * fromUnit.toMeters;

    // Convert from meters to target unit
    return valueInMeters / toUnit.toMeters;
  }

  // Handle clear button press
  void _handleClear() {
    if (widget.readOnly || widget.disabled) return;

    setState(() {
      _value = 0.0;
      _controller.clear();
      _hasError = false;
      _errorText = null;
    });

    if (widget.onClear != null) {
      widget.onClear!();
    }

    if (widget.onChanged != null) {
      widget.onChanged!(_value, _unit);
    }
  }

  // Handle unit change
  void _handleUnitChange(DistanceUnit? newUnit) {
    if (newUnit == null || widget.readOnly || widget.disabled) return;

    setState(() {
      _unit = newUnit;
    });

    if (widget.onUnitChanged != null) {
      widget.onUnitChanged!(newUnit);
    }

    if (widget.onChanged != null) {
      widget.onChanged!(_value, _unit);
    }
  }

  // Build the unit selector dropdown
  // Widget _buildUnitSelector() {
  //   final units = widget.availableUnits ?? DistanceUnit.values;

  //   return DropdownButton<DistanceUnit>(
  //     value: _unit,
  //     onChanged: widget.readOnly || widget.disabled ? null : _handleUnitChange,
  //     items: units.map((unit) {
  //       return DropdownMenuItem<DistanceUnit>(
  //         value: unit,
  //         child: Text(
  //           unit.symbol,
  //           style: TextStyle(
  //             color: widget.dropdownTextColor ?? widget.textColor,
  //             fontSize: widget.fontSize,
  //             fontWeight: widget.fontWeight,
  //             fontFamily: widget.fontFamily,
  //           ),
  //         ),
  //       );
  //     }).toList(),
  //     icon: Icon(
  //       Icons.arrow_drop_down,
  //       color: widget.dropdownIconColor ?? (widget.disabled ? Colors.grey : null),
  //     ),
  //     style: TextStyle(
  //       color: widget.dropdownTextColor ?? widget.textColor,
  //       fontSize: widget.fontSize,
  //       fontWeight: widget.fontWeight,
  //       fontFamily: widget.fontFamily,
  //     ),
  //     dropdownColor: widget.dropdownBackgroundColor ?? widget.backgroundColor,
  //     underline: Container(
  //       height: 0,
  //     ),
  //   );
  // }

  // Build the conversion result display
  Widget _buildConversionResult() {
    if (!widget.showConversion ||
        widget.conversionUnit == null ||
        !widget.showConversionResult) {
      return const SizedBox.shrink();
    }

    final convertedValue = _convertValue(_value, _unit, widget.conversionUnit!);
    final formattedValue = _formatValue(convertedValue);

    return Row(
      children: [
        if (widget.showConversionLabel)
          Text(
            widget.conversionLabel,
            // style: TextStyle(
            //   color: widget.conversionResultColor,
            //   fontSize: widget.conversionResultFontSize,
            // ),
            style: FontManager.getCustomStyle(
              fontFamily: FontManager.fontFamilyInter,
              fontWeight: FontManager.medium,
              color: widget.conversionResultColor,
              fontSize: _getResponsiveValueFontSize(context),
            ),
          ),
        const SizedBox(width: 0.0),
        Text(
          '$formattedValue ${widget.conversionUnit!.symbol}',
          // style: TextStyle(
          //   color: widget.conversionResultColor,
          //   fontSize: widget.conversionResultFontSize,
          //   fontWeight: FontWeight.bold,
          // ),
          style: FontManager.getCustomStyle(
            fontFamily: FontManager.fontFamilyInter,
            fontWeight: FontManager.medium,
            color: widget.conversionResultColor,
            fontSize: _getResponsiveValueFontSize(context),
          ),
        ),
      ],
    );
  }

  // Build the increment/decrement buttons
  // Widget _buildButtons() {
  //   if (!widget.showButtons) {
  //     return const SizedBox.shrink();
  //   }

  //   return Row(
  //     mainAxisSize: MainAxisSize.min,
  //     children: [
  //       IconButton(
  //         icon: Icon(widget.decrementIcon),
  //         onPressed: widget.readOnly || widget.disabled ? null : _decrement,
  //         color: widget.buttonIconColor,
  //         iconSize: widget.buttonSize * 0.5,
  //         padding: EdgeInsets.zero,
  //         constraints: BoxConstraints(
  //           minWidth: widget.buttonSize,
  //           minHeight: widget.buttonSize,
  //         ),
  //         style: IconButton.styleFrom(
  //           backgroundColor: widget.buttonColor,
  //           shape: RoundedRectangleBorder(
  //             borderRadius: BorderRadius.circular(widget.borderRadius),
  //           ),
  //         ),
  //       ),
  //       const SizedBox(width: 4.0),
  //       IconButton(
  //         icon: Icon(widget.incrementIcon),
  //         onPressed: widget.readOnly || widget.disabled ? null : _increment,
  //         color: widget.buttonIconColor,
  //         iconSize: widget.buttonSize * 0.5,
  //         padding: EdgeInsets.zero,
  //         constraints: BoxConstraints(
  //           minWidth: widget.buttonSize,
  //           minHeight: widget.buttonSize,
  //         ),
  //         style: IconButton.styleFrom(
  //           backgroundColor: widget.buttonColor,
  //           shape: RoundedRectangleBorder(
  //             borderRadius: BorderRadius.circular(widget.borderRadius),
  //           ),
  //         ),
  //       ),
  //     ],
  //   );
  // }

  @override
  Widget build(BuildContext context) {
    // Determine the error text to display
    final displayErrorText = _hasError ? _errorText : widget.errorText;

    // Build the input field with the dropdown inside the input (as suffix)
    Widget inputField = MouseRegion(
      onEnter: (_) {
        setState(() {
          _isHovered = true;
        });
      },
      onExit: (_) {
        setState(() {
          _isHovered = false;
        });
      },
      child: Container(
        height: _getResponsiveHeight(context),
        child: Theme(
          data: Theme.of(context).copyWith(
            inputDecorationTheme: const InputDecorationTheme(
              hoverColor: Colors.transparent,
              focusColor: Colors.transparent,
            ),
          ),
          child: TextField(
            controller: _controller,
            focusNode: _focusNode,
            keyboardType: const TextInputType.numberWithOptions(
              decimal: true,
              signed: true,
            ),
            textAlign: widget.textAlign,
            textAlignVertical: TextAlignVertical.center,
            // style: TextStyle(
            //   color: widget.disabled ? Colors.grey : widget.textColor,
            //   fontSize: _getResponsiveValueFontSize(context),
            //   fontWeight: widget.fontWeight,
            //   fontFamily: widget.fontFamily,
            // ),
            style: FontManager.getCustomStyle(
              fontFamily: FontManager.fontFamilyInter,
              fontWeight: FontManager.medium,
              color: widget.disabled ? Colors.grey : Color(0xFF333333),
              fontSize: _getResponsiveValueFontSize(context),
            ),
            decoration: InputDecoration(
              hintText: widget.hint,
              helperText: widget.helperText,
              errorText: displayErrorText,
              //hintStyle: TextStyle(color: widget.hintColor),
              labelStyle: FontManager.getCustomStyle(
                fontFamily: FontManager.fontFamilyInter,
                fontWeight: FontManager.medium,
                color: widget.hintColor,
                fontSize: _getResponsiveValueFontSize(context),
              ),
              //helperStyle: TextStyle(color: widget.helperTextColor),
              helperStyle: FontManager.getCustomStyle(
                fontFamily: FontManager.fontFamilyInter,
                fontWeight: FontManager.medium,
                color: widget.helperTextColor,
                fontSize: _getResponsiveValueFontSize(context),
              ),
              //errorStyle: TextStyle(color: widget.errorTextColor),
              errorStyle: FontManager.getCustomStyle(
                fontFamily: FontManager.fontFamilyInter,
                fontWeight: FontManager.medium,
                color: widget.errorTextColor,
                fontSize: _getResponsiveValueFontSize(context),
              ),
              filled: true,
              fillColor: widget.backgroundColor,
              border:
                  widget.hasBorder
                      ? OutlineInputBorder(
                        borderRadius: BorderRadius.circular(
                          widget.borderRadius,
                        ),
                        borderSide: BorderSide(
                          color: widget.borderColor,
                          width: widget.borderWidth,
                        ),
                      )
                      : InputBorder.none,
              enabledBorder:
                  widget.hasBorder
                      ? OutlineInputBorder(
                        borderRadius: BorderRadius.circular(
                          widget.borderRadius,
                        ),
                        borderSide: BorderSide(
                          color:
                              _isHovered
                                  ? (widget.hoverColor ??
                                      const Color(0xFF0058FF))
                                  : widget.borderColor,
                          width: _isHovered ? 1.0 : widget.borderWidth,
                        ),
                      )
                      : null,
              focusedBorder:
                  widget.hasBorder
                      ? OutlineInputBorder(
                        borderRadius: BorderRadius.circular(
                          widget.borderRadius,
                        ),
                        borderSide: BorderSide(
                          color: widget.focusColor ?? const Color(0xFF0058FF),
                          width: 1.0,
                        ),
                      )
                      : null,
              errorBorder:
                  widget.hasBorder
                      ? OutlineInputBorder(
                        borderRadius: BorderRadius.circular(
                          widget.borderRadius,
                        ),
                        borderSide: BorderSide(
                          color: widget.errorBorderColor,
                          width: widget.errorBorderWidth,
                        ),
                      )
                      : null,
              focusedErrorBorder:
                  widget.hasBorder
                      ? OutlineInputBorder(
                        borderRadius: BorderRadius.circular(
                          widget.borderRadius,
                        ),
                        borderSide: BorderSide(
                          color: widget.errorBorderColor,
                          width: widget.errorBorderWidth,
                        ),
                      )
                      : null,
              contentPadding: _getResponsivePadding(context),
              suffixIcon: DropdownButtonHideUnderline(
                child: PopupMenuButton<DistanceUnit>(
                  onSelected: (DistanceUnit unit) {
                    _handleUnitChange(unit);
                  },
                  itemBuilder:
                      (BuildContext context) =>
                          [
                            DistanceUnit.centimeter,
                            DistanceUnit.millimeter,
                            DistanceUnit.meter,
                            DistanceUnit.kilometer,
                          ].map((unit) {
                            return PopupMenuItem<DistanceUnit>(
                              value: unit,
                              padding: EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 0,
                              ), // No default padding!
                              height: 32, // Compact!
                              child: Text(
                                unit.symbol.toUpperCase(),
                                // style: TextStyle(
                                //   fontSize: _getResponsiveValueFontSize(
                                //     context,
                                //   ),
                                //   fontWeight: FontWeight.w400,
                                // ),
                                style: FontManager.getCustomStyle(
                                  fontFamily: FontManager.fontFamilyInter,
                                  fontWeight: FontManager.medium,
                                  color: const Color(0xFF333333),
                                  fontSize: _getResponsiveValueFontSize(
                                    context,
                                  ),
                                ),
                              ),
                            );
                          }).toList(),
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 6),

                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          _unit.symbol.toUpperCase(),
                          // style: TextStyle(
                          //   fontSize: _getResponsiveValueFontSize(context),
                          // ),
                          style: FontManager.getCustomStyle(
                            fontFamily: FontManager.fontFamilyInter,
                            fontWeight: FontManager.regular,
                            fontSize: _getResponsiveValueFontSize(context),
                            color: const Color(0xFF333333),
                          ),
                        ),
                        Icon(
                          Icons.arrow_drop_down,
                          size: _getResponsiveIconSize(context),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            readOnly: widget.readOnly,
            enabled: !widget.disabled,
            inputFormatters: [
              if (widget.allowNegative)
                FilteringTextInputFormatter.allow(RegExp(r'[0-9.,\-]'))
              else
                FilteringTextInputFormatter.allow(RegExp(r'[0-9.,]')),
            ],
            onChanged: (text) {
              if (widget.readOnly || widget.disabled) return;

              final newValue = _parseValue(text);
              setState(() {
                _value = newValue;
                _hasError = false;
                _errorText = null;
              });

              if (widget.onChanged != null) {
                widget.onChanged!(newValue, _unit);
              }
            },
            onSubmitted: (text) {
              if (widget.readOnly || widget.disabled) return;

              _validateAndFormat();

              if (widget.onSubmitted != null) {
                widget.onSubmitted!(_value, _unit);
              }
            },
            onTap: widget.onTap,
            cursorColor: widget.cursorColor,
            cursorWidth: widget.cursorWidth,
            cursorHeight: widget.cursorHeight,
            cursorRadius: widget.cursorRadius,
          ),
        ),
      ),
    );

    // Check if we need to show conversion result
    final hasConversionResult =
        widget.showConversion &&
        widget.conversionUnit != null &&
        widget.showConversionResult;

    // If no conversion result, return simple layout
    if (!hasConversionResult) {
      return IntrinsicHeight(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [Expanded(child: inputField)],
        ),
      );
    }

    // If conversion result needed, use column layout
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        IntrinsicHeight(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [Expanded(child: inputField)],
          ),
        ),
        _buildConversionResult(),
      ],
    );
  }
}

// Helper class for number formatting
class NumberFormat {
  int minimumFractionDigits = 0;
  int maximumFractionDigits = 0;

  static NumberFormat decimalPattern() {
    return NumberFormat();
  }

  String format(double value) {
    // Format the number with the specified decimal places
    String formatted = value.toStringAsFixed(maximumFractionDigits);

    // Add thousands separators
    final parts = formatted.split('.');
    final wholePart = parts[0];
    final decimalPart = parts.length > 1 ? parts[1] : '';

    final formattedWholePart = _addThousandsSeparators(wholePart);

    if (decimalPart.isEmpty && minimumFractionDigits == 0) {
      return formattedWholePart;
    } else {
      return '$formattedWholePart.$decimalPart';
    }
  }

  String _addThousandsSeparators(String value) {
    final result = StringBuffer();
    final length = value.length;

    for (int i = 0; i < length; i++) {
      if (i > 0 && (length - i) % 3 == 0 && value[i - 1] != '-') {
        result.write(',');
      }
      result.write(value[i]);
    }

    return result.toString();
  }
}

double _getResponsiveHeight(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 56.0; // Extra Large (>1920px)
  } else if (screenWidth >= 1440) {
    return 48.0; // Large (1440-1920px)
  } else if (screenWidth >= 1280) {
    return 40.0; // Medium (1280-1366px)
  } else if (screenWidth >= 768) {
    return 32.0; // Small (768-1024px)
  } else {
    return 32.0; // Default for very small screens
  }
}

double _getResponsiveIconSize(BuildContext context) {
  final double screenWidth = MediaQuery.of(context).size.width;
  if (screenWidth > 1920) {
    return 18.0; // Extra Large
  } else if (screenWidth >= 1440) {
    return 18.0; // Large
  } else if (screenWidth >= 1280) {
    return 18.0; // Medium
  } else if (screenWidth >= 768) {
    return 16.0; // Small
  } else {
    return 16.0; // Extra Small (fallback for very small screens)
  }
}

double _getResponsiveValueFontSize(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 18.0; // Extra Large
  } else if (screenWidth >= 1440) {
    return 16.0; // Large
  } else if (screenWidth >= 1280) {
    return 14.0; // Medium
  } else {
    return 14.0; // Default for very small screens
  }
}

EdgeInsets _getResponsivePadding(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth >= 1440) {
    return const EdgeInsets.symmetric(
      horizontal: 16.0,
      vertical: 4.0,
    ); // Extra Large
  } else if (screenWidth >= 1280) {
    return const EdgeInsets.symmetric(horizontal: 12.0, vertical: 3.0); // Large
  } else if (screenWidth >= 768) {
    return const EdgeInsets.symmetric(horizontal: 8.0, vertical: 2.0); // Medium
  } else {
    return const EdgeInsets.symmetric(
      horizontal: 6.0,
      vertical: 1.0,
    ); // Default for very small screens
  }
}
