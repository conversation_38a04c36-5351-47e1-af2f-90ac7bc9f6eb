import 'package:flutter/material.dart';
import 'dart:convert';
import '../utils/callback_interpreter.dart';

/// A configurable encrypted text input widget that securely handles sensitive text.
class EncryptedTextWidget extends StatefulWidget {
  // Basic properties
  final String initialValue;
  final bool isRequired;
  final int? minLength;
  final int? maxLength;

  // Appearance properties
  final Color textColor;
  final Color backgroundColor;
  final Color borderColor;
  final double borderWidth;
  final double borderRadius;
  final bool hasBorder;
  final double fontSize;
  final FontWeight fontWeight;
  final bool isCompact;
  final bool hasShadow;
  final double elevation;
  final bool isDarkTheme;
  final TextAlign textAlign;

  // Label properties
  final String? label;
  final String? hint;
  final String? helperText;
  final String? errorText;

  // Icon properties
  final bool showPrefix;
  final IconData? prefixIcon;
  final bool showSuffix;
  final IconData? suffixIcon;
  final bool showToggleVisibility;

  // Behavior properties
  final bool isReadOnly;
  final bool isDisabled;
  final bool autofocus;
  final bool hasAnimation;
  final bool autoValidate;
  final bool showClearButton;
  final bool obscureText;
  final String obscuringCharacter;

  // Layout properties
  final double width;
  final double height;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;

  // Callback
  final Function(String)? onChanged;
  final Function(String)? onSubmitted;

  // Advanced interaction properties
  final void Function(bool)? onHover;
  final void Function(bool)? onFocus;
  final FocusNode? focusNode;
  final Color? hoverColor;
  final Color? focusColor;
  final bool enableFeedback;
  final VoidCallback? onTap;
  final VoidCallback? onDoubleTap;
  final VoidCallback? onLongPress;

  // JSON configuration properties
  final Map<String, dynamic>? jsonCallbacks;
  final bool useJsonCallbacks;
  final Map<String, dynamic>? callbackState;
  final Map<String, Function>? customCallbackHandlers;
  final Map<String, dynamic>? jsonConfig;
  final bool useJsonValidation;
  final bool useJsonStyling;
  final bool useJsonFormatting;

  // Security-specific JSON configuration
  final bool useJsonSecurity;
  final Map<String, dynamic>? securityConfig;

  const EncryptedTextWidget({
    super.key,
    this.initialValue = '',
    this.isRequired = false,
    this.minLength,
    this.maxLength,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.borderRadius = 4.0,
    this.hasBorder = true,
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.normal,
    this.isCompact = false,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.isDarkTheme = false,
    this.textAlign = TextAlign.start,
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.showPrefix = true,
    this.prefixIcon = Icons.lock,
    this.showSuffix = false,
    this.suffixIcon,
    this.showToggleVisibility = true,
    this.isReadOnly = false,
    this.isDisabled = false,
    this.autofocus = false,
    this.hasAnimation = false,
    this.autoValidate = true,
    this.showClearButton = false,
    this.obscureText = true,
    this.obscuringCharacter = '•',
    this.width = double.infinity,
    this.height = 0,
    this.padding = const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
    this.margin = const EdgeInsets.all(0),
    this.onChanged,
    this.onSubmitted,
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.hoverColor,
    this.focusColor,
    this.enableFeedback = true,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    // JSON configuration properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
    this.useJsonValidation = false,
    this.useJsonStyling = false,
    this.useJsonFormatting = false,
    // Security-specific JSON configuration
    this.useJsonSecurity = false,
    this.securityConfig,
  });

  /// Creates an EncryptedTextWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the EncryptedTextWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "initialValue": "securePassword123",
  ///   "isRequired": true,
  ///   "label": "Password",
  ///   "hint": "Enter your password",
  ///   "obscureText": true,
  ///   "showToggleVisibility": true,
  ///   "minLength": 8
  /// }
  /// ```
  factory EncryptedTextWidget.fromJson(Map<String, dynamic> json) {
    // Parse colors
    Color? parseColor(dynamic colorValue) {
      if (colorValue == null) return null;

      if (colorValue is String) {
        // Handle hex strings like "#FF0000"
        if (colorValue.startsWith('#')) {
          String hexColor = colorValue.substring(1);

          // Handle shorthand hex like #RGB
          if (hexColor.length == 3) {
            hexColor = hexColor.split('').map((c) => '$c$c').join('');
          }

          // Add alpha channel if missing
          if (hexColor.length == 6) {
            hexColor = 'FF$hexColor';
          }

          // Parse the hex value
          try {
            return Color(int.parse('0x$hexColor'));
          } catch (e) {
            // Silently handle the error and return null
            return null;
          }
        }

        // Handle named colors
        switch (colorValue.toLowerCase()) {
          case 'red':
            return Colors.red;
          case 'blue':
            return Colors.blue;
          case 'green':
            return Colors.green;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'brown':
            return Colors.brown;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          case 'amber':
            return Colors.amber;
          case 'cyan':
            return Colors.cyan;
          case 'indigo':
            return Colors.indigo;
          case 'lime':
            return Colors.lime;
          case 'teal':
            return Colors.teal;
          default:
            return null;
        }
      } else if (colorValue is int) {
        // Handle integer color values
        return Color(colorValue);
      }

      return null;
    }

    // Parse text alignment
    TextAlign parseTextAlign(dynamic alignValue) {
      if (alignValue == null) return TextAlign.start;

      if (alignValue is String) {
        switch (alignValue.toLowerCase()) {
          case 'center':
            return TextAlign.center;
          case 'end':
          case 'right':
            return TextAlign.end;
          case 'start':
          case 'left':
            return TextAlign.start;
          case 'justify':
            return TextAlign.justify;
          default:
            return TextAlign.start;
        }
      }

      return TextAlign.start;
    }

    // Parse font weight
    FontWeight parseFontWeight(dynamic weightValue) {
      if (weightValue == null) return FontWeight.normal;

      if (weightValue is String) {
        switch (weightValue.toLowerCase()) {
          case 'bold':
            return FontWeight.bold;
          case 'normal':
            return FontWeight.normal;
          case 'light':
            return FontWeight.w300;
          default:
            return FontWeight.normal;
        }
      } else if (weightValue is int) {
        switch (weightValue) {
          case 100:
            return FontWeight.w100;
          case 200:
            return FontWeight.w200;
          case 300:
            return FontWeight.w300;
          case 400:
            return FontWeight.w400;
          case 500:
            return FontWeight.w500;
          case 600:
            return FontWeight.w600;
          case 700:
            return FontWeight.w700;
          case 800:
            return FontWeight.w800;
          case 900:
            return FontWeight.w900;
          default:
            return FontWeight.normal;
        }
      } else if (weightValue is bool && weightValue) {
        return FontWeight.bold;
      }

      return FontWeight.normal;
    }

    // Parse icon data
    IconData? parseIconData(dynamic iconValue) {
      if (iconValue == null) return null;

      if (iconValue is String) {
        switch (iconValue.toLowerCase()) {
          case 'lock':
            return Icons.lock;
          case 'lock_open':
            return Icons.lock_open;
          case 'lock_outline':
            return Icons.lock_outline;
          case 'security':
            return Icons.security;
          case 'enhanced_encryption':
            return Icons.enhanced_encryption;
          case 'password':
            return Icons.password;
          case 'key':
            return Icons.key;
          case 'vpn_key':
            return Icons.vpn_key;
          case 'visibility':
            return Icons.visibility;
          case 'visibility_off':
            return Icons.visibility_off;
          case 'email':
            return Icons.email;
          case 'person':
            return Icons.person;
          case 'account_circle':
            return Icons.account_circle;
          case 'add':
            return Icons.add;
          case 'remove':
            return Icons.remove;
          case 'clear':
            return Icons.clear;
          case 'delete':
            return Icons.delete;
          case 'edit':
            return Icons.edit;
          case 'save':
            return Icons.save;
          case 'check':
            return Icons.check;
          case 'close':
            return Icons.close;
          case 'search':
            return Icons.search;
          case 'settings':
            return Icons.settings;
          case 'info':
            return Icons.info;
          case 'warning':
            return Icons.warning;
          case 'error':
            return Icons.error;
          case 'help':
            return Icons.help;
          case 'copy':
            return Icons.copy;
          case 'paste':
            return Icons.paste;
          default:
            return null;
        }
      }

      return null;
    }

    // Parse edge insets
    EdgeInsetsGeometry parseEdgeInsets(dynamic insetsValue) {
      if (insetsValue == null) {
        return const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0);
      }

      if (insetsValue is Map<String, dynamic>) {
        final double left = (insetsValue['left'] as num?)?.toDouble() ?? 0.0;
        final double top = (insetsValue['top'] as num?)?.toDouble() ?? 0.0;
        final double right = (insetsValue['right'] as num?)?.toDouble() ?? 0.0;
        final double bottom =
            (insetsValue['bottom'] as num?)?.toDouble() ?? 0.0;

        if (insetsValue.containsKey('all')) {
          final double all = (insetsValue['all'] as num).toDouble();
          return EdgeInsets.all(all);
        } else if (insetsValue.containsKey('horizontal') ||
            insetsValue.containsKey('vertical')) {
          final double horizontal =
              (insetsValue['horizontal'] as num?)?.toDouble() ?? 0.0;
          final double vertical =
              (insetsValue['vertical'] as num?)?.toDouble() ?? 0.0;
          return EdgeInsets.symmetric(
            horizontal: horizontal,
            vertical: vertical,
          );
        } else {
          return EdgeInsets.fromLTRB(left, top, right, bottom);
        }
      } else if (insetsValue is num) {
        return EdgeInsets.all(insetsValue.toDouble());
      }

      return const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0);
    }

    // Parse JSON callback properties
    Map<String, dynamic>? jsonCallbacks;
    bool useJsonCallbacks = json['useJsonCallbacks'] as bool? ?? false;

    if (json['callbacks'] != null) {
      if (json['callbacks'] is Map) {
        jsonCallbacks = Map<String, dynamic>.from(json['callbacks'] as Map);
        useJsonCallbacks = true;
      } else if (json['callbacks'] is String) {
        try {
          jsonCallbacks =
              jsonDecode(json['callbacks'] as String) as Map<String, dynamic>;
          useJsonCallbacks = true;
        } catch (e) {
          // Silently handle the error
        }
      }
    }

    // Parse additional callback properties for specific events
    if (json['onChanged'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onChanged'] = json['onChanged'];
      useJsonCallbacks = true;
    }

    if (json['onSubmitted'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onSubmitted'] = json['onSubmitted'];
      useJsonCallbacks = true;
    }

    if (json['onTap'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onTap'] = json['onTap'];
      useJsonCallbacks = true;
    }

    if (json['onDoubleTap'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onDoubleTap'] = json['onDoubleTap'];
      useJsonCallbacks = true;
    }

    if (json['onLongPress'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onLongPress'] = json['onLongPress'];
      useJsonCallbacks = true;
    }

    if (json['onHover'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onHover'] = json['onHover'];
      useJsonCallbacks = true;
    }

    if (json['onFocus'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onFocus'] = json['onFocus'];
      useJsonCallbacks = true;
    }

    if (json['onToggleVisibility'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onToggleVisibility'] = json['onToggleVisibility'];
      useJsonCallbacks = true;
    }

    // Parse security-specific configuration
    Map<String, dynamic>? securityConfig;
    bool useJsonSecurity = json['useJsonSecurity'] as bool? ?? false;

    if (json['securityConfig'] != null) {
      if (json['securityConfig'] is Map) {
        securityConfig = Map<String, dynamic>.from(
          json['securityConfig'] as Map,
        );
        useJsonSecurity = true;
      } else if (json['securityConfig'] is String) {
        try {
          securityConfig =
              jsonDecode(json['securityConfig'] as String)
                  as Map<String, dynamic>;
          useJsonSecurity = true;
        } catch (e) {
          // Silently handle the error
        }
      }
    }

    // Create the widget with all properties from JSON
    return EncryptedTextWidget(
      initialValue: json['initialValue'] as String? ?? '',
      isRequired: json['isRequired'] as bool? ?? false,
      minLength: json['minLength'] as int?,
      maxLength: json['maxLength'] as int?,
      textColor: parseColor(json['textColor']) ?? Colors.black,
      backgroundColor: parseColor(json['backgroundColor']) ?? Colors.white,
      borderColor: parseColor(json['borderColor']) ?? Colors.grey,
      borderWidth:
          json['borderWidth'] != null
              ? (json['borderWidth'] as num).toDouble()
              : 1.0,
      borderRadius:
          json['borderRadius'] != null
              ? (json['borderRadius'] as num).toDouble()
              : 4.0,
      hasBorder: json['hasBorder'] as bool? ?? true,
      fontSize:
          json['fontSize'] != null
              ? (json['fontSize'] as num).toDouble()
              : 16.0,
      fontWeight: parseFontWeight(json['fontWeight']),
      isCompact: json['isCompact'] as bool? ?? false,
      hasShadow: json['hasShadow'] as bool? ?? false,
      elevation:
          json['elevation'] != null
              ? (json['elevation'] as num).toDouble()
              : 2.0,
      isDarkTheme: json['isDarkTheme'] as bool? ?? false,
      textAlign: parseTextAlign(json['textAlign']),
      label: json['label'] as String?,
      hint: json['hint'] as String?,
      helperText: json['helperText'] as String?,
      errorText: json['errorText'] as String?,
      showPrefix: json['showPrefix'] as bool? ?? true,
      prefixIcon: parseIconData(json['prefixIcon']) ?? Icons.lock,
      showSuffix: json['showSuffix'] as bool? ?? false,
      suffixIcon: parseIconData(json['suffixIcon']),
      showToggleVisibility: json['showToggleVisibility'] as bool? ?? true,
      isReadOnly: json['isReadOnly'] as bool? ?? false,
      isDisabled: json['isDisabled'] as bool? ?? false,
      autofocus: json['autofocus'] as bool? ?? false,
      hasAnimation: json['hasAnimation'] as bool? ?? false,
      autoValidate: json['autoValidate'] as bool? ?? true,
      showClearButton: json['showClearButton'] as bool? ?? false,
      obscureText: json['obscureText'] as bool? ?? true,
      obscuringCharacter: json['obscuringCharacter'] as String? ?? '•',
      width:
          json['width'] != null
              ? (json['width'] as num).toDouble()
              : double.infinity,
      height: json['height'] != null ? (json['height'] as num).toDouble() : 0,
      padding: parseEdgeInsets(json['padding']),
      margin: parseEdgeInsets(json['margin']),
      // Advanced interaction properties
      hoverColor: parseColor(json['hoverColor']),
      focusColor: parseColor(json['focusColor']),
      enableFeedback: json['enableFeedback'] as bool? ?? true,
      // JSON configuration properties
      jsonCallbacks: jsonCallbacks,
      useJsonCallbacks: useJsonCallbacks,
      callbackState:
          json['callbackState'] != null
              ? Map<String, dynamic>.from(json['callbackState'] as Map)
              : {},
      jsonConfig: json,
      useJsonValidation: json['useJsonValidation'] as bool? ?? false,
      useJsonStyling: json['useJsonStyling'] as bool? ?? false,
      useJsonFormatting: json['useJsonFormatting'] as bool? ?? false,
      // Security-specific JSON configuration
      useJsonSecurity: useJsonSecurity,
      securityConfig: securityConfig,
    );
  }

  /// Converts the widget configuration to a JSON map
  ///
  /// This method allows for serializing the widget's configuration to JSON,
  /// which can be useful for saving configurations or sending them to a server.
  Map<String, dynamic> toJson() {
    // Convert color to hex string
    String? colorToHex(Color? color) {
      if (color == null) return null;

      // Convert to hex format
      final r = color.red;
      final g = color.green;
      final b = color.blue;
      return '#${r.toRadixString(16).padLeft(2, '0')}${g.toRadixString(16).padLeft(2, '0')}${b.toRadixString(16).padLeft(2, '0')}';

      // Note: The above code uses deprecated APIs but is kept for compatibility.
      // A better approach would be to use the newer APIs like color.r, color.g, color.b
      // but that would require a minimum Flutter version that supports these APIs.
    }

    // Convert text alignment to string
    String textAlignToString(TextAlign align) {
      switch (align) {
        case TextAlign.center:
          return 'center';
        case TextAlign.end:
          return 'end';
        case TextAlign.left:
          return 'left';
        case TextAlign.right:
          return 'right';
        case TextAlign.justify:
          return 'justify';
        case TextAlign.start:
          return 'start';
      }
    }

    // Convert font weight to int
    int fontWeightToInt(FontWeight weight) {
      if (weight == FontWeight.w100) return 100;
      if (weight == FontWeight.w200) return 200;
      if (weight == FontWeight.w300) return 300;
      if (weight == FontWeight.w400 || weight == FontWeight.normal) return 400;
      if (weight == FontWeight.w500) return 500;
      if (weight == FontWeight.w600) return 600;
      if (weight == FontWeight.w700 || weight == FontWeight.bold) return 700;
      if (weight == FontWeight.w800) return 800;
      if (weight == FontWeight.w900) return 900;
      return 400;
    }

    // Convert icon data to string
    String? iconDataToString(IconData? icon) {
      if (icon == null) return null;

      if (icon == Icons.lock) return 'lock';
      if (icon == Icons.lock_open) return 'lock_open';
      if (icon == Icons.lock_outline) return 'lock_outline';
      if (icon == Icons.security) return 'security';
      if (icon == Icons.enhanced_encryption) return 'enhanced_encryption';
      if (icon == Icons.password) return 'password';
      if (icon == Icons.key) return 'key';
      if (icon == Icons.vpn_key) return 'vpn_key';
      if (icon == Icons.visibility) return 'visibility';
      if (icon == Icons.visibility_off) return 'visibility_off';
      if (icon == Icons.email) return 'email';
      if (icon == Icons.person) return 'person';
      if (icon == Icons.account_circle) return 'account_circle';
      if (icon == Icons.add) return 'add';
      if (icon == Icons.remove) return 'remove';
      if (icon == Icons.clear) return 'clear';
      if (icon == Icons.delete) return 'delete';
      if (icon == Icons.edit) return 'edit';
      if (icon == Icons.save) return 'save';
      if (icon == Icons.check) return 'check';
      if (icon == Icons.close) return 'close';
      if (icon == Icons.search) return 'search';
      if (icon == Icons.settings) return 'settings';
      if (icon == Icons.info) return 'info';
      if (icon == Icons.warning) return 'warning';
      if (icon == Icons.error) return 'error';
      if (icon == Icons.help) return 'help';
      if (icon == Icons.copy) return 'copy';
      if (icon == Icons.paste) return 'paste';

      return null;
    }

    // Convert edge insets to map
    Map<String, dynamic>? edgeInsetsToMap(EdgeInsetsGeometry insets) {
      if (insets is EdgeInsets) {
        if (insets.left == insets.top &&
            insets.left == insets.right &&
            insets.left == insets.bottom) {
          return {'all': insets.left};
        } else if (insets.left == insets.right && insets.top == insets.bottom) {
          return {'horizontal': insets.left, 'vertical': insets.top};
        } else {
          return {
            'left': insets.left,
            'top': insets.top,
            'right': insets.right,
            'bottom': insets.bottom,
          };
        }
      }
      return null;
    }

    // Create the JSON map
    final Map<String, dynamic> json = {
      'initialValue': initialValue,
      'isRequired': isRequired,
      'minLength': minLength,
      'maxLength': maxLength,
      'textColor': colorToHex(textColor),
      'backgroundColor': colorToHex(backgroundColor),
      'borderColor': colorToHex(borderColor),
      'borderWidth': borderWidth,
      'borderRadius': borderRadius,
      'hasBorder': hasBorder,
      'fontSize': fontSize,
      'fontWeight': fontWeightToInt(fontWeight),
      'isCompact': isCompact,
      'hasShadow': hasShadow,
      'elevation': elevation,
      'isDarkTheme': isDarkTheme,
      'textAlign': textAlignToString(textAlign),
      'label': label,
      'hint': hint,
      'helperText': helperText,
      'errorText': errorText,
      'showPrefix': showPrefix,
      'prefixIcon': iconDataToString(prefixIcon),
      'showSuffix': showSuffix,
      'suffixIcon': iconDataToString(suffixIcon),
      'showToggleVisibility': showToggleVisibility,
      'isReadOnly': isReadOnly,
      'isDisabled': isDisabled,
      'autofocus': autofocus,
      'hasAnimation': hasAnimation,
      'autoValidate': autoValidate,
      'showClearButton': showClearButton,
      'obscureText': obscureText,
      'obscuringCharacter': obscuringCharacter,
      'width': width == double.infinity ? 'infinity' : width,
      'height': height,
      'padding': edgeInsetsToMap(padding),
      'margin': edgeInsetsToMap(margin),
      'hoverColor': colorToHex(hoverColor),
      'focusColor': colorToHex(focusColor),
      'enableFeedback': enableFeedback,
      'useJsonCallbacks': useJsonCallbacks,
      'useJsonValidation': useJsonValidation,
      'useJsonStyling': useJsonStyling,
      'useJsonFormatting': useJsonFormatting,
      'useJsonSecurity': useJsonSecurity,
    };

    // Add callbacks if they exist
    if (jsonCallbacks != null && jsonCallbacks!.isNotEmpty) {
      json['callbacks'] = jsonCallbacks;
    }

    // Add security config if it exists
    if (securityConfig != null && securityConfig!.isNotEmpty) {
      json['securityConfig'] = securityConfig;
    }

    return json;
  }

  @override
  State<EncryptedTextWidget> createState() => _EncryptedTextWidgetState();
}

class _EncryptedTextWidgetState extends State<EncryptedTextWidget>
    with SingleTickerProviderStateMixin {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  late AnimationController _animationController;
  late Animation<double> _animation;

  String? _errorText;
  bool _isValid = true;
  bool _obscureText = true;
  bool _isHovered = false; // Track hover state
  bool _isFocused = false; // Track focus state

  // Map to store dynamic state for callbacks
  Map<String, dynamic> _callbackState = {};

  // Map to store parsed configuration from JSON
  Map<String, dynamic>? _parsedJsonConfig;

  // Map to store security configuration from JSON
  Map<String, dynamic>? _securityConfig;

  @override
  void initState() {
    super.initState();

    // Initialize controller with initial value
    _controller = TextEditingController(text: widget.initialValue);

    // Initialize focus node
    _focusNode = widget.focusNode ?? FocusNode();

    // Add focus listener to track focus changes
    _focusNode.addListener(() {
      setState(() {
        _isFocused = _focusNode.hasFocus;
      });
      if (widget.onFocus != null) {
        widget.onFocus!(_focusNode.hasFocus);
      }
    });

    // Initialize callback state
    _callbackState =
        widget.callbackState != null
            ? Map<String, dynamic>.from(widget.callbackState!)
            : {};

    // Parse JSON configuration if provided
    if (widget.jsonConfig != null) {
      _parsedJsonConfig = Map<String, dynamic>.from(widget.jsonConfig!);

      // Apply initial JSON validation if enabled
      if (widget.useJsonValidation) {
        _applyJsonValidation();
      }

      // Apply initial JSON styling if enabled
      if (widget.useJsonStyling) {
        _applyJsonStyling();
      }

      // Apply initial JSON formatting if enabled
      if (widget.useJsonFormatting) {
        _applyJsonFormatting();
      }
    }

    // Parse security configuration if provided
    if (widget.securityConfig != null) {
      _securityConfig = Map<String, dynamic>.from(widget.securityConfig!);

      // Apply initial security configuration if enabled
      if (widget.useJsonSecurity) {
        _applySecurityConfig();
      }
    }

    // Initialize animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // Create animation
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    // Start animation if needed
    if (widget.hasAnimation) {
      _animationController.forward();
    }

    // Set initial error text
    _errorText = widget.errorText;

    // Set initial obscure text state
    _obscureText = widget.obscureText;

    // Validate initial value
    if (widget.autoValidate && widget.initialValue.isNotEmpty) {
      if (widget.useJsonValidation) {
        _applyJsonValidation();
      } else {
        _validateInput(widget.initialValue);
      }
    }

    // Execute onInit callback if defined in JSON
    _executeJsonCallback('onInit');
  }

  /// Executes a callback defined in JSON
  ///
  /// This method interprets and executes a callback defined in the JSON configuration.
  /// It supports various callback types and provides access to the current state.
  void _executeJsonCallback(String callbackType, [dynamic data]) {
    if (!widget.useJsonCallbacks || widget.jsonCallbacks == null) return;

    // Check if the callback exists in the JSON configuration
    if (widget.jsonCallbacks!.containsKey(callbackType)) {
      final callback = widget.jsonCallbacks![callbackType];

      // Update the callback state with the current value
      _callbackState['currentValue'] = _controller.text;
      _callbackState['isValid'] = _isValid;
      _callbackState['isObscured'] = _obscureText;

      // If data is provided, prepare it for the callback
      dynamic callbackValue;
      if (data != null) {
        callbackValue = data;
        _callbackState['data'] = data.toString();
      } else {
        callbackValue = _controller.text;
      }

      // Execute the callback using the CallbackInterpreter
      try {
        CallbackInterpreter.executeCallback(
          callback,
          context,
          value: callbackValue,
          state: _callbackState,
          customHandlers: widget.customCallbackHandlers,
        );
      } catch (e) {
        debugPrint('Error executing JSON callback: $e');
      }
    }
  }

  /// Applies JSON validation rules to the current value
  ///
  /// This method applies validation rules defined in the JSON configuration.
  void _applyJsonValidation() {
    if (_parsedJsonConfig == null || !widget.useJsonValidation) return;

    final value = _controller.text;

    // Example: Apply validation rules
    if (_parsedJsonConfig!.containsKey('validationRules')) {
      final rules = _parsedJsonConfig!['validationRules'];

      if (rules is Map<String, dynamic>) {
        // Apply required validation
        if (rules.containsKey('required') && rules['required'] == true) {
          if (value.isEmpty) {
            _isValid = false;
            _errorText = 'This field is required';
            return;
          }
        }

        // Apply min length validation
        if (rules.containsKey('minLength') && rules['minLength'] is int) {
          final minLength = rules['minLength'] as int;
          if (value.length < minLength) {
            _isValid = false;
            _errorText = 'Minimum length is $minLength characters';
            return;
          }
        }

        // Apply max length validation
        if (rules.containsKey('maxLength') && rules['maxLength'] is int) {
          final maxLength = rules['maxLength'] as int;
          if (value.length > maxLength) {
            _isValid = false;
            _errorText = 'Maximum length is $maxLength characters';
            return;
          }
        }

        // Apply regex validation
        if (rules.containsKey('regex') && rules['regex'] is String) {
          final regex = RegExp(rules['regex'] as String);
          if (!regex.hasMatch(value)) {
            _isValid = false;
            _errorText =
                rules['regexErrorMessage'] as String? ?? 'Invalid format';
            return;
          }
        }

        // Apply password strength validation
        if (rules.containsKey('passwordStrength') &&
            rules['passwordStrength'] is String) {
          final strength = rules['passwordStrength'] as String;

          switch (strength.toLowerCase()) {
            case 'weak':
              // At least 6 characters
              if (value.length < 6) {
                _isValid = false;
                _errorText = 'Password must be at least 6 characters';
                return;
              }
              break;
            case 'medium':
              // At least 8 characters with at least one number
              if (value.length < 8) {
                _isValid = false;
                _errorText = 'Password must be at least 8 characters';
                return;
              }
              if (!RegExp(r'[0-9]').hasMatch(value)) {
                _isValid = false;
                _errorText = 'Password must contain at least one number';
                return;
              }
              break;
            case 'strong':
              // At least 8 characters with at least one number, one uppercase, and one special character
              if (value.length < 8) {
                _isValid = false;
                _errorText = 'Password must be at least 8 characters';
                return;
              }
              if (!RegExp(r'[0-9]').hasMatch(value)) {
                _isValid = false;
                _errorText = 'Password must contain at least one number';
                return;
              }
              if (!RegExp(r'[A-Z]').hasMatch(value)) {
                _isValid = false;
                _errorText =
                    'Password must contain at least one uppercase letter';
                return;
              }
              if (!RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(value)) {
                _isValid = false;
                _errorText =
                    'Password must contain at least one special character';
                return;
              }
              break;
          }
        }

        // Apply custom validation
        if (rules.containsKey('custom') && rules['custom'] is String) {
          final customRule = rules['custom'] as String;

          // Example: Check if value contains a specific string
          if (customRule.startsWith('contains:')) {
            final substring = customRule.substring('contains:'.length);
            if (!value.contains(substring)) {
              _isValid = false;
              _errorText = 'Value must contain: $substring';
              return;
            }
          }

          // Example: Check if value starts with a specific prefix
          if (customRule.startsWith('startsWith:')) {
            final prefix = customRule.substring('startsWith:'.length);
            if (!value.startsWith(prefix)) {
              _isValid = false;
              _errorText = 'Value must start with: $prefix';
              return;
            }
          }
        }
      }
    }

    _isValid = true;
    _errorText = widget.errorText;
  }

  /// Applies JSON styling to the widget
  ///
  /// This method applies styling rules defined in the JSON configuration.
  void _applyJsonStyling() {
    if (_parsedJsonConfig == null || !widget.useJsonStyling) return;

    // This would be implemented to apply dynamic styling from JSON
    // Not fully implemented in this example
  }

  /// Applies JSON formatting to the current value
  ///
  /// This method applies formatting rules defined in the JSON configuration.
  void _applyJsonFormatting() {
    if (_parsedJsonConfig == null || !widget.useJsonFormatting) return;

    // This would be implemented to apply dynamic formatting from JSON
    // Not fully implemented in this example
  }

  /// Applies security configuration to the widget
  ///
  /// This method applies security rules defined in the JSON configuration.
  void _applySecurityConfig() {
    if (_securityConfig == null || !widget.useJsonSecurity) return;

    // Example: Apply security rules
    if (_securityConfig!.containsKey('obscureText')) {
      _obscureText = _securityConfig!['obscureText'] as bool? ?? true;
    }

    if (_securityConfig!.containsKey('obscuringCharacter')) {
      // This would be implemented to change the obscuring character
      // Not fully implemented in this example
    }

    if (_securityConfig!.containsKey('preventScreenCapture')) {
      // This would be implemented to prevent screen capture
      // Not fully implemented in this example
    }

    if (_securityConfig!.containsKey('preventCopyPaste')) {
      // This would be implemented to prevent copy/paste
      // Not fully implemented in this example
    }

    if (_securityConfig!.containsKey('autoLockTimeout')) {
      // This would be implemented to auto-lock the field after a timeout
      // Not fully implemented in this example
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    _animationController.dispose();
    super.dispose();
  }

  /// Validates the input and updates the error text
  bool _validateInput(String value) {
    if (value.isEmpty) {
      if (widget.isRequired) {
        setState(() {
          _errorText = 'This field is required';
          _isValid = false;
        });
        return false;
      } else {
        setState(() {
          _errorText = widget.errorText;
          _isValid = true;
        });
        return true;
      }
    }

    if (widget.minLength != null && value.length < widget.minLength!) {
      setState(() {
        _errorText = 'Minimum length is ${widget.minLength} characters';
        _isValid = false;
      });
      return false;
    }

    setState(() {
      _errorText = widget.errorText;
      _isValid = true;
    });
    return true;
  }

  /// Handles changes to the input
  void _handleValueChanged(String value) {
    // Execute onBeforeChange callback if defined in JSON
    _executeJsonCallback('onBeforeChange', value);

    // Validate the input
    if (widget.autoValidate) {
      if (widget.useJsonValidation) {
        _applyJsonValidation();
      } else {
        _validateInput(value);
      }
    }

    // Call standard callback
    if (widget.onChanged != null) {
      widget.onChanged!(value);
    }

    // Execute onChanged callback if defined in JSON
    _executeJsonCallback('onChanged', value);

    // Reset animation if needed
    if (widget.hasAnimation) {
      _animationController.reset();
      _animationController.forward();
    }
  }

  /// Clears the input field
  void _clearInput() {
    // Execute onBeforeClear callback if defined in JSON
    _executeJsonCallback('onBeforeClear');

    _controller.clear();
    _handleValueChanged('');

    // Execute onClear callback if defined in JSON
    _executeJsonCallback('onClear');
  }

  /// Toggles the visibility of the text
  void _toggleVisibility() {
    // Execute onBeforeToggleVisibility callback if defined in JSON
    _executeJsonCallback('onBeforeToggleVisibility', _obscureText);

    setState(() {
      _obscureText = !_obscureText;
    });

    // Execute onToggleVisibility callback if defined in JSON
    _executeJsonCallback('onToggleVisibility', _obscureText);
  }

  @override
  Widget build(BuildContext context) {
    // Apply dark theme if specified
    final effectiveTextColor =
        widget.isDarkTheme ? Colors.white : widget.textColor;
    final effectiveBackgroundColor =
        widget.isDarkTheme ? Colors.grey.shade800 : widget.backgroundColor;
    final effectiveBorderColor =
        widget.isDarkTheme ? Colors.grey.shade600 : Color(0xFFCCCCCC);

    // Determine icon color based on state (hover, focus, error)
    Color effectiveIconColor;
    if (!_isValid) {
      effectiveIconColor = Colors.red; // Error state
    } else if (_isFocused) {
      effectiveIconColor = const Color(0xFF0058FF); // Focus state
    } else if (_isHovered) {
      effectiveIconColor = const Color(0xFF0058FF); // Hover state
    } else {
      effectiveIconColor = const Color(0xFFCCCCCC); // Default state
    }

    // Create suffix icons based on configuration
    List<Widget> suffixIcons = [];

    if (widget.showToggleVisibility) {
      suffixIcons.add(
        IconButton(
          icon: Icon(
            _obscureText ? Icons.visibility_off : Icons.visibility,
            size: 18,
            color: effectiveIconColor,
          ),
          onPressed: _toggleVisibility,
          tooltip: _obscureText ? 'Show' : 'Hide',
        ),
      );
    }

    if (widget.showClearButton && !widget.isReadOnly && !widget.isDisabled) {
      suffixIcons.add(
        IconButton(
          icon: Icon(Icons.clear, size: 18, color: effectiveIconColor),
          onPressed: _clearInput,
          tooltip: 'Clear',
        ),
      );
    }

    if (widget.showSuffix && widget.suffixIcon != null) {
      suffixIcons.add(Icon(widget.suffixIcon, color: effectiveIconColor));
    }

    // Create the suffix widget based on the number of icons
    Widget? suffixWidget;
    if (suffixIcons.isNotEmpty) {
      if (suffixIcons.length == 1) {
        suffixWidget = suffixIcons.first;
      } else {
        suffixWidget = Row(
          mainAxisSize: MainAxisSize.min,
          children: suffixIcons,
        );
      }
    }

    // Create the text field with all the specified properties
    Widget textField = Container(
      height: _getResponsiveHeight(context),
      child: TextField(
        controller: _controller,
        focusNode: _focusNode,
        keyboardType: TextInputType.visiblePassword,
        textAlign: widget.textAlign,
        style: TextStyle(
          //color: widget.isDisabled ? Colors.grey : effectiveTextColor,
          color: widget.isDisabled ? Colors.grey : Color(0xFF333333),
          //fontSize: widget.fontSize,
          fontSize: _getResponsiveInputFontSize(context),
          fontWeight: widget.fontWeight,
        ),
        obscureText: _obscureText,
        obscuringCharacter: widget.obscuringCharacter,
        maxLength: widget.maxLength,
        decoration: InputDecoration(
          //labelText: widget.label,
          hintText: widget.hint ?? 'Enter secure text',
          helperText: widget.helperText,
          errorText: _errorText,
          filled: true,
          fillColor:
              widget.isDisabled
                  ? Colors.grey.shade200
                  : effectiveBackgroundColor,
          hoverColor:
              widget.isDisabled
                  ? Colors.grey.shade200
                  : effectiveBackgroundColor, // Remove hover background change
          // prefixIcon:
          //     widget.showPrefix && widget.prefixIcon != null
          //         ? Icon(widget.prefixIcon, color: effectiveIconColor)
          //         : null,
          suffixIcon: suffixWidget,
          // contentPadding:
          //     widget.isCompact
          //         ? const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0)
          //         : widget.padding,
          contentPadding: _getResponsivePadding(context),
          border:
              widget.hasBorder
                  ? OutlineInputBorder(
                    borderRadius: BorderRadius.circular(widget.borderRadius),
                    borderSide: BorderSide(
                      color: effectiveBorderColor,
                      width: widget.borderWidth,
                    ),
                  )
                  : InputBorder.none,
          enabledBorder:
              widget.hasBorder
                  ? OutlineInputBorder(
                    borderRadius: BorderRadius.circular(widget.borderRadius),
                    borderSide: BorderSide(
                      color:
                          _isHovered
                              ? const Color(0xFF0058FF)
                              : effectiveBorderColor,
                      width: widget.borderWidth,
                    ),
                  )
                  : InputBorder.none,
          focusedBorder:
              widget.hasBorder
                  ? OutlineInputBorder(
                    borderRadius: BorderRadius.circular(widget.borderRadius),
                    borderSide: BorderSide(
                      //color: Theme.of(context).primaryColor,
                      //width: widget.borderWidth + 0.5,
                      color: Color(0xFF0058FF),
                    ),
                  )
                  : InputBorder.none,
          errorBorder:
              widget.hasBorder
                  ? OutlineInputBorder(
                    borderRadius: BorderRadius.circular(widget.borderRadius),
                    borderSide: BorderSide(
                      color: Colors.red,
                      width: widget.borderWidth,
                    ),
                  )
                  : InputBorder.none,
        ),
        enabled: !widget.isDisabled && !widget.isReadOnly,
        readOnly: widget.isReadOnly,
        autofocus: widget.autofocus,
        onChanged: _handleValueChanged,
        onSubmitted: (value) {
          // Execute onBeforeSubmit callback if defined in JSON
          _executeJsonCallback('onBeforeSubmit', value);

          // Call standard callback
          if (widget.onSubmitted != null) {
            widget.onSubmitted!(value);
          }

          // Execute onSubmitted callback if defined in JSON
          _executeJsonCallback('onSubmitted', value);
        },
        onTap: () {
          // Execute onBeforeTap callback if defined in JSON
          _executeJsonCallback('onBeforeTap');

          if (widget.isCompact) {
            // Select all text when tapped in compact mode
            _controller.selection = TextSelection(
              baseOffset: 0,
              extentOffset: _controller.text.length,
            );
          }

          // Call standard callback
          if (widget.onTap != null) {
            widget.onTap!();
          }

          // Execute onTap callback if defined in JSON
          _executeJsonCallback('onTap');
        },
      ),
    );

    // Apply animation if needed
    final animatedWidget =
        widget.hasAnimation
            ? FadeTransition(opacity: _animation, child: textField)
            : textField;

    // Apply shadow if needed
    final shadowWidget =
        widget.hasShadow
            ? Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(widget.borderRadius),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(25),
                    blurRadius: widget.elevation,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: animatedWidget,
            )
            : animatedWidget;

    // Create the final widget with the specified size
    final sizedWidget = Container(
      width: widget.width,
      //height: widget.height > 0 ? widget.height : null,
      //height: _getResponsiveHeight(context),
      //padding: _getResponsivePadding(context),
      margin: widget.margin,
      child: shadowWidget,
    );

    // Wrap with MouseRegion to handle hover events
    return MouseRegion(
      onEnter: (_) {
        setState(() {
          _isHovered = true;
        });
        if (widget.onHover != null) {
          widget.onHover!(true);
        }
      },
      onExit: (_) {
        setState(() {
          _isHovered = false;
        });
        if (widget.onHover != null) {
          widget.onHover!(false);
        }
      },
      child: sizedWidget,
    );
  }

  EdgeInsets _getResponsivePadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth >= 1440) {
      return const EdgeInsets.symmetric(
        horizontal: 16.0,
        vertical: 4.0,
      ); // Extra Large
    } else if (screenWidth >= 1280) {
      return const EdgeInsets.symmetric(
        horizontal: 12.0,
        vertical: 3.0,
      ); // Large
    } else if (screenWidth >= 768) {
      return const EdgeInsets.symmetric(
        horizontal: 8.0,
        vertical: 2.0,
      ); // Medium
    } else {
      return const EdgeInsets.symmetric(
        horizontal: 6.0,
        vertical: 1.0,
      ); // Default for very small screens
    }
  }

  double _getResponsiveFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 18.0; // Extra Large (>1920px)
    } else if (screenWidth >= 1440) {
      return 16.0; // Large (1440-1920px)
    } else if (screenWidth >= 1280) {
      return 12.0; // Medium (1280-1366px)
    } else if (screenWidth >= 768) {
      return 12.0; // Small (768-1024px)
    } else {
      return 12.0; // Default for very small screens
    }
  }

  double _getResponsiveInputFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 18.0; // Extra Large (>1920px) - Reduced for better fit
    } else if (screenWidth >= 1440) {
      return 16.0; // Large (1440-1920px) - Reduced for better fit
    } else if (screenWidth >= 1280) {
      return 16.0; // Medium (1280-1366px) - Standard size
    } else if (screenWidth >= 768) {
      return 14.0; // Small (768-1024px) - Increased for readability
    } else {
      return 14.0; // Default for very small screens - Consistent
    }
  }

  double _getResponsiveHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 56.0; // Extra Large (>1920px)
    } else if (screenWidth >= 1440) {
      return 48.0; // Large (1440-1920px)
    } else if (screenWidth >= 1280) {
      return 40.0; // Medium (1280-1366px)
    } else if (screenWidth >= 768) {
      return 32.0; // Small (768-1024px)
    } else {
      return 32.0; // Default for very small screens
    }
  }
}
