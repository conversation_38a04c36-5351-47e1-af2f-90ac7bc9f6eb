import 'package:flutter/material.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/models/nsl_heirarchy_model1.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/models/nsl_tree_side_details_new.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/models/metric_info_model.dart';
import 'package:nsl/providers/base_provider.dart';
import 'package:nsl/services/nsl_hierarchy_api_service.dart';
import 'package:nsl/services/nsl_hierarchy_data_transformer.dart';


class NSLHierarchyMobileProvider extends BaseProvider {
  // Core hierarchy data
  List<NSLHierarchyData1> _nslNodes = [];
  NSLNode? _rootNode;
  NSLNode? _filteredRootNode;
  
  // Selection and expansion state
  NSLHierarchyData1? _selectedNode;
  String? _selectedNodeId;
  final Set<String> _expandedNodes = {};
  
  // Panel state
  bool _showBottomPanel = false;
  
  // Search state
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  
  // API data
  NslTreeSidePanel? _nodeDetails;
  bool _isLoadingNodeDetails = false;
  MetricsInfo? _nodeTransactions;
  bool _isLoadingTransactions = false;
  Map<String, dynamic>? _systemInfo;

  // Getters
  List<NSLHierarchyData1> get nslNodes => _nslNodes;
  NSLNode? get rootNode => _rootNode;
  NSLNode? get filteredRootNode => _filteredRootNode;
  NSLHierarchyData1? get selectedNode => _selectedNode;
  String? get selectedNodeId => _selectedNodeId;
  Set<String> get expandedNodes => _expandedNodes;
  bool get showBottomPanel => _showBottomPanel;
  TextEditingController get searchController => _searchController;
  String get searchQuery => _searchQuery;
  NslTreeSidePanel? get nodeDetails => _nodeDetails;
  bool get isLoadingNodeDetails => _isLoadingNodeDetails;
  MetricsInfo? get nodeTransactions => _nodeTransactions;
  bool get isLoadingTransactions => _isLoadingTransactions;
  Map<String, dynamic>? get systemInfo => _systemInfo;
  bool get hasError => error != null;

  NSLHierarchyMobileProvider() {
    _searchController.addListener(_onSearchChanged);
    loadNSLData();
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    _searchQuery = _searchController.text.trim();
    _buildFilteredTree();
    notifyListeners();
  }

  // Core data loading
  Future<void> loadNSLData() async {
    await runWithLoadingAndErrorHandling<void>(
      () async {
        // Fetch data from API
        final apiResponse = await NslHierarchyApiService.fetchModules();
        
        if (apiResponse == null) {
          throw Exception('Failed to fetch data from API');
        }

        // Transform API data to hierarchy structure
        _rootNode = NslHierarchyDataTransformer.transformApiDataToHierarchy(apiResponse);
        
        if (_rootNode == null) {
          throw Exception('Failed to transform API data to hierarchy');
        }

        if (_rootNode != null) {
          _nslNodes = _rootNode!.originalData.getAllNodes();
          // Initialize with root node expanded
          _expandedNodes.add(_rootNode!.id);
        }

        // Store system info (if available in the response)
        _systemInfo = {}; // Initialize empty for now

        // Build filtered tree
        _buildFilteredTree();
      },
      context: 'NSLHierarchyMobileProvider.loadNSLData',
    );
  }

  void _buildFilteredTree() {
    if (_searchQuery.isEmpty) {
      _filteredRootNode = _rootNode;
    } else {
      final foundNodes = _nslNodes
          .where((node) =>
              node.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
              node.id.toLowerCase().contains(_searchQuery.toLowerCase()))
          .toList();

      if (foundNodes.isNotEmpty) {
        _filteredRootNode = _findNodeInTree(_rootNode, foundNodes.first.id);
      } else {
        _filteredRootNode = null;
      }
    }
  }

  NSLNode? _findNodeInTree(NSLNode? node, String targetId) {
    if (node == null) return null;
    
    if (node.id == targetId) {
      return node;
    }
    
    for (var child in node.children) {
      final found = _findNodeInTree(child, targetId);
      if (found != null) {
        return found;
      }
    }
    
    return null;
  }

  // Node interaction methods
  void onNodeInfoTap(String nodeId) {
    if (_expandedNodes.contains(nodeId)) {
      // If the clicked node is already expanded, collapse it and all its descendants
      _collapseNodeAndDescendants(nodeId);
    } else {
      // Sibling accordion behavior: collapse siblings but keep parent path
      _expandNodeWithSiblingAccordion(nodeId);
    }
    notifyListeners();
  }

  void _collapseNodeAndDescendants(String nodeId) {
    // Remove the node and all its descendants from expanded nodes
    final nodeToCollapse = _findNodeInTree(_rootNode, nodeId);
    if (nodeToCollapse != null) {
      _removeNodeAndDescendants(nodeToCollapse);
    }
  }

  void _removeNodeAndDescendants(NSLNode node) {
    _expandedNodes.remove(node.id);
    for (var child in node.children) {
      _removeNodeAndDescendants(child);
    }
  }

  void _expandNodeWithSiblingAccordion(String nodeId) {
    final nodeToExpand = _findNodeInTree(_rootNode, nodeId);
    if (nodeToExpand == null) return;

    // Find the parent of the node to expand
    final parentNode = _findParentNode(_rootNode, nodeId);
    
    if (parentNode != null) {
      // Collapse all siblings of the node to expand
      for (var sibling in parentNode.children) {
        if (sibling.id != nodeId) {
          _removeNodeAndDescendants(sibling);
        }
      }
    }

    // Ensure the path from root to this node is expanded
    _ensurePathToNodeExpanded(nodeId);
    
    // Expand the clicked node
    _expandedNodes.add(nodeId);
  }

  NSLNode? _findParentNode(NSLNode? node, String targetId) {
    if (node == null) return null;
    
    // Check if any direct child has the target ID
    for (var child in node.children) {
      if (child.id == targetId) {
        return node;
      }
    }
    
    // Recursively search in children
    for (var child in node.children) {
      final parent = _findParentNode(child, targetId);
      if (parent != null) {
        return parent;
      }
    }
    
    return null;
  }

  void _ensurePathToNodeExpanded(String nodeId) {
    final pathToNode = _getPathToNodeIds(_rootNode, nodeId);
    for (var pathNodeId in pathToNode) {
      _expandedNodes.add(pathNodeId);
    }
  }

  List<String> _getPathToNodeIds(NSLNode? node, String targetId, [List<String>? currentPath]) {
    if (node == null) return [];
    
    currentPath ??= [];
    currentPath.add(node.id);
    
    if (node.id == targetId) {
      return List.from(currentPath);
    }
    
    for (var child in node.children) {
      final path = _getPathToNodeIds(child, targetId, List.from(currentPath));
      if (path.isNotEmpty) {
        return path;
      }
    }
    
    return [];
  }

  void onNodeCircleTap(NSLHierarchyData1 nodeData) {
    _selectedNode = nodeData;
    _selectedNodeId = nodeData.id;
    _showBottomPanel = true;
    
    // Fetch node details and transactions
    _fetchNodeDetails(nodeData.id);
    _fetchNodeTransactions(nodeData.id);
    
    notifyListeners();
  }

  void hideBottomPanel() {
    _showBottomPanel = false;
    _selectedNode = null;
    _selectedNodeId = null;
    notifyListeners();
  }

  // Search methods
  void clearSearch() {
    _searchController.clear();
  }

  // API methods
  Future<void> _fetchNodeDetails(String nodeId) async {
    _isLoadingNodeDetails = true;
    notifyListeners();

    try {
      _nodeDetails = await NslHierarchyApiService.fetchNodeDetails(nodeId);
    } catch (e) {
      // Error handling is done by BaseProvider
      _nodeDetails = null;
    } finally {
      _isLoadingNodeDetails = false;
      notifyListeners();
    }
  }

  Future<void> _fetchNodeTransactions(String nodeId) async {
    _isLoadingTransactions = true;
    notifyListeners();

    try {
      // Get current time and 30 days ago for default date range
      final now = DateTime.now();
      final thirtyDaysAgo = now.subtract(const Duration(days: 30));
      final fromUnixSeconds = thirtyDaysAgo.millisecondsSinceEpoch ~/ 1000;
      final toUnixSeconds = now.millisecondsSinceEpoch ~/ 1000;

      _nodeTransactions = await NslHierarchyApiService.fetchNodeTransactions(
        nodeId,
        fromUnixSeconds,
        toUnixSeconds
      );
    } catch (e) {
      // Error handling is done by BaseProvider
      _nodeTransactions = null;
    } finally {
      _isLoadingTransactions = false;
      notifyListeners();
    }
  }

  // Date range change handler
  void onDateRangeChanged(Map<String, dynamic> newDateRange) {
    if (_selectedNodeId != null) {
      _fetchNodeTransactions(_selectedNodeId!);
    }
  }
}
