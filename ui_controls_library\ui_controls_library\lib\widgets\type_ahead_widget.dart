import 'package:flutter/material.dart';
import 'package:ui_controls_library/widgets/utils/font_manager.dart';

/// A customizable type-ahead (autocomplete) widget.
///
/// This widget provides a text input field with autocomplete functionality,
/// showing suggestions as the user types. It supports various customization
/// options including suggestion appearance, behavior, and styling.
class TypeAheadWidget<T> extends StatefulWidget {
  /// List of suggestions to display
  final List<T> suggestions;

  /// Function to extract display text from suggestion objects
  final String Function(T)? displayTextExtractor;

  /// Function to extract value from suggestion objects
  final String Function(T)? valueExtractor;

  /// Initial text in the input field
  final String initialValue;

  /// Placeholder text when the input is empty
  final String hintText;

  /// Label text for the input field
  final String? labelText;

  /// Helper text displayed below the input field
  final String? helperText;

  /// Error text displayed below the input field (shows only if not null)
  final String? errorText;

  /// Whether the input field is enabled
  final bool enabled;

  /// Whether the input field is required
  final bool required;

  /// Minimum number of characters before showing suggestions
  final int minCharsForSuggestions;

  /// Maximum number of suggestions to show
  final int maxSuggestions;

  /// Whether to show the clear button
  final bool showClearButton;

  /// Whether to automatically correct user input
  final bool autoCorrect;

  /// Whether to automatically capitalize sentences
  final bool autoCapitalize;

  /// Whether to show suggestions on focus (without typing)
  final bool showSuggestionsOnFocus;

  /// Whether to allow custom values not in the suggestions list
  final bool allowCustomValues;

  /// Whether to sort suggestions alphabetically
  final bool sortAlphabetically;

  /// Whether to highlight matching text in suggestions
  final bool highlightMatches;

  /// Color of the input field border
  final Color? borderColor;

  /// Color of the input field when focused
  final Color? focusColor;

  /// Color of the suggestion items
  final Color? suggestionColor;

  /// Color of the highlighted text in suggestions
  final Color? highlightColor;

  /// Background color of the suggestion list
  final Color? suggestionBackgroundColor;

  /// Border radius of the input field
  final double borderRadius;

  /// Border radius of the suggestion list
  final double suggestionBorderRadius;

  /// Width of the input field
  final double? width;

  /// Height of each suggestion item
  final double suggestionItemHeight;

  /// Callback when a suggestion is selected (returns the full object)
  final Function(T)? onSuggestionSelected;

  /// Callback when the input value changes (returns the display text)
  final Function(String)? onChanged;

  /// Callback when the input is submitted (returns the display text)
  final Function(String)? onSubmitted;

  // Advanced Interaction Properties

  /// Callback for mouse hover
  ///
  /// This function is called when the mouse pointer enters or exits the widget.
  /// The parameter is true when the mouse enters and false when it exits.
  final void Function(bool)? onHover;

  /// Callback for keyboard focus
  ///
  /// This function is called when the widget gains or loses keyboard focus.
  /// The parameter is true when focus is gained and false when it is lost.
  final void Function(bool)? onFocus;

  /// Focus node for manual focus control
  ///
  /// This allows for programmatic control of the widget's focus state.
  /// If null, the widget will create and manage its own focus node.
  final FocusNode? focusNode;

  /// Whether the widget should be focused automatically when it appears
  ///
  /// If true, the widget will request focus when it is first built.
  final bool autofocus;

  /// Color when the widget is hovered
  final Color? hoverColor;

  /// Tooltip text to display on hover
  final String? tooltip;

  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Whether to enable haptic/audio feedback
  ///
  /// If true, the widget will provide haptic and/or audio feedback when interacted with.
  final bool enableFeedback;

  /// Callback for tap gesture
  ///
  /// This function is called when the widget is tapped.
  final VoidCallback? onTap;

  /// Callback for double tap gesture
  ///
  /// This function is called when the widget is double-tapped.
  final VoidCallback? onDoubleTap;

  /// Callback for long press gesture
  ///
  /// This function is called when the widget is long-pressed.
  final VoidCallback? onLongPress;

  final bool? showSuffixButton;

  const TypeAheadWidget({
    super.key,
    this.suggestions = const [],
    this.displayTextExtractor,
    this.valueExtractor,
    this.initialValue = '',
    this.hintText = 'Type to search...',
    this.labelText,
    this.helperText,
    this.errorText,
    this.enabled = true,
    this.required = false,
    this.minCharsForSuggestions = 1,
    this.maxSuggestions = 5,
    this.showClearButton = true,
    this.autoCorrect = true,
    this.autoCapitalize = true,
    this.showSuggestionsOnFocus = false,
    this.allowCustomValues = true,
    this.sortAlphabetically = false,
    this.highlightMatches = true,
    this.borderColor,
    this.focusColor,
    this.suggestionColor,
    this.highlightColor,
    this.suggestionBackgroundColor,
    this.borderRadius = 8.0,
    this.suggestionBorderRadius = 8.0,
    this.width,
    this.suggestionItemHeight = 48.0,
    this.onSuggestionSelected,
    this.onChanged,
    this.onSubmitted,
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.autofocus = false,
    this.hoverColor,
    this.tooltip,
    this.semanticsLabel,
    this.enableFeedback = true,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    this.showSuffixButton,
  });

  /// Creates a TypeAheadWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the TypeAheadWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "suggestions": ["Apple", "Banana", "Cherry"],
  ///   "initialValue": "A",
  ///   "hintText": "Search fruits...",
  ///   "labelText": "Fruit",
  ///   "minCharsForSuggestions": 1,
  ///   "maxSuggestions": 5,
  ///   "highlightMatches": true
  /// }
  /// ```
  static TypeAheadWidget<String> fromJson(Map<String, dynamic> json) {
    // Parse colors
    Color? parseColor(dynamic colorValue) {
      if (colorValue == null) return null;

      if (colorValue is String) {
        // Handle named colors
        switch (colorValue.toLowerCase()) {
          case 'red':
            return Colors.red;
          case 'blue':
            return Colors.blue;
          case 'green':
            return Colors.green;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'brown':
            return Colors.brown;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          case 'transparent':
            return Colors.transparent;
          case 'teal':
            return Colors.teal;
          case 'cyan':
            return Colors.cyan;
          case 'amber':
            return Colors.amber;
          case 'indigo':
            return Colors.indigo;
          case 'lime':
            return Colors.lime;
          default:
            // Handle hex colors
            if (colorValue.startsWith('#')) {
              try {
                final hexColor = colorValue.substring(1);
                final hexValue = int.parse(
                  '0xFF${hexColor.padRight(8, 'F').substring(0, 8)}',
                );
                return Color(hexValue);
              } catch (e) {
                return null;
              }
            }
            return null;
        }
      } else if (colorValue is int) {
        return Color(colorValue);
      }

      return null;
    }

    // Parse suggestions
    List<String> parseSuggestions(dynamic suggestionsValue) {
      if (suggestionsValue == null) return [];

      if (suggestionsValue is List) {
        return suggestionsValue.map((item) => item.toString()).toList();
      } else if (suggestionsValue is String) {
        return suggestionsValue.split(',').map((item) => item.trim()).toList();
      }

      return [];
    }

    return TypeAheadWidget<String>(
      suggestions: parseSuggestions(json['suggestions']),
      initialValue: json['initialValue'] as String? ?? '',
      hintText: json['hintText'] as String? ?? 'Type to search...',
      labelText: json['labelText'] as String?,
      helperText: json['helperText'] as String?,
      errorText: json['errorText'] as String?,
      enabled: json['enabled'] as bool? ?? true,
      required: json['required'] as bool? ?? false,
      minCharsForSuggestions: json['minCharsForSuggestions'] as int? ?? 1,
      maxSuggestions: json['maxSuggestions'] as int? ?? 5,
      showClearButton: json['showClearButton'] as bool? ?? true,
      autoCorrect: json['autoCorrect'] as bool? ?? true,
      autoCapitalize: json['autoCapitalize'] as bool? ?? true,
      showSuggestionsOnFocus: json['showSuggestionsOnFocus'] as bool? ?? false,
      allowCustomValues: json['allowCustomValues'] as bool? ?? true,
      sortAlphabetically: json['sortAlphabetically'] as bool? ?? false,
      highlightMatches: json['highlightMatches'] as bool? ?? true,
      borderColor: parseColor(json['borderColor']),
      focusColor: parseColor(json['focusColor']),
      suggestionColor: parseColor(json['suggestionColor']),
      highlightColor: parseColor(json['highlightColor']),
      suggestionBackgroundColor: parseColor(json['suggestionBackgroundColor']),
      borderRadius: (json['borderRadius'] as num?)?.toDouble() ?? 8.0,
      suggestionBorderRadius:
          (json['suggestionBorderRadius'] as num?)?.toDouble() ?? 8.0,
      width: (json['width'] as num?)?.toDouble(),
      suggestionItemHeight:
          (json['suggestionItemHeight'] as num?)?.toDouble() ?? 48.0,
      onSuggestionSelected:
          json['onSuggestionSelected'] == true
              ? (suggestion) {
                debugPrint('Suggestion selected: $suggestion');
              }
              : null,
      onChanged:
          json['onChanged'] == true
              ? (value) {
                debugPrint('Value changed: $value');
              }
              : null,
      onSubmitted:
          json['onSubmitted'] == true
              ? (value) {
                debugPrint('Value submitted: $value');
              }
              : null,
      onHover:
          json['onHover'] == true
              ? (isHovered) {
                debugPrint('Hover state: $isHovered');
              }
              : null,
      onFocus:
          json['onFocus'] == true
              ? (isFocused) {
                debugPrint('Focus state: $isFocused');
              }
              : null,
      focusNode: null, // Cannot be created from JSON
      autofocus: json['autofocus'] as bool? ?? false,
      hoverColor: parseColor(json['hoverColor']),
      tooltip: json['tooltip'] as String?,
      semanticsLabel: json['semanticsLabel'] as String?,
      enableFeedback: json['enableFeedback'] as bool? ?? true,
      onTap:
          json['onTap'] == true
              ? () {
                debugPrint('Widget tapped');
              }
              : null,
      onDoubleTap:
          json['onDoubleTap'] == true
              ? () {
                debugPrint('Widget double-tapped');
              }
              : null,
      onLongPress:
          json['onLongPress'] == true
              ? () {
                debugPrint('Widget long-pressed');
              }
              : null,
    );
  }

  /// Converts the TypeAheadWidget to a JSON map
  ///
  /// This method allows for serializing the widget configuration to JSON,
  /// which can be useful for saving configurations or sharing them.
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {
      'suggestions': suggestions,
      'initialValue': initialValue,
      'hintText': hintText,
      'enabled': enabled,
      'required': required,
      'minCharsForSuggestions': minCharsForSuggestions,
      'maxSuggestions': maxSuggestions,
      'showClearButton': showClearButton,
      'autoCorrect': autoCorrect,
      'autoCapitalize': autoCapitalize,
      'showSuggestionsOnFocus': showSuggestionsOnFocus,
      'allowCustomValues': allowCustomValues,
      'sortAlphabetically': sortAlphabetically,
      'highlightMatches': highlightMatches,
      'borderRadius': borderRadius,
      'suggestionBorderRadius': suggestionBorderRadius,
      'suggestionItemHeight': suggestionItemHeight,
      'autofocus': autofocus,
      'enableFeedback': enableFeedback,
    };

    // Add optional properties
    if (labelText != null) json['labelText'] = labelText;
    if (helperText != null) json['helperText'] = helperText;
    if (errorText != null) json['errorText'] = errorText;
    if (width != null) json['width'] = width;
    if (tooltip != null) json['tooltip'] = tooltip;
    if (semanticsLabel != null) json['semanticsLabel'] = semanticsLabel;

    // Add colors
    if (borderColor != null) json['borderColor'] = _colorToString(borderColor!);
    if (focusColor != null) json['focusColor'] = _colorToString(focusColor!);
    if (suggestionColor != null)
      json['suggestionColor'] = _colorToString(suggestionColor!);
    if (highlightColor != null)
      json['highlightColor'] = _colorToString(highlightColor!);
    if (suggestionBackgroundColor != null)
      json['suggestionBackgroundColor'] = _colorToString(
        suggestionBackgroundColor!,
      );
    if (hoverColor != null) json['hoverColor'] = _colorToString(hoverColor!);

    // Add callback flags
    if (onSuggestionSelected != null) json['onSuggestionSelected'] = true;
    if (onChanged != null) json['onChanged'] = true;
    if (onSubmitted != null) json['onSubmitted'] = true;
    if (onHover != null) json['onHover'] = true;
    if (onFocus != null) json['onFocus'] = true;
    if (onTap != null) json['onTap'] = true;
    if (onDoubleTap != null) json['onDoubleTap'] = true;
    if (onLongPress != null) json['onLongPress'] = true;

    return json;
  }

  /// Helper method to convert a Color to a string
  static String _colorToString(Color color) {
    // Handle standard colors by name for better readability
    if (color == Colors.red) return 'red';
    if (color == Colors.blue) return 'blue';
    if (color == Colors.green) return 'green';
    if (color == Colors.yellow) return 'yellow';
    if (color == Colors.orange) return 'orange';
    if (color == Colors.purple) return 'purple';
    if (color == Colors.pink) return 'pink';
    if (color == Colors.brown) return 'brown';
    if (color == Colors.grey) return 'grey';
    if (color == Colors.black) return 'black';
    if (color == Colors.white) return 'white';
    if (color == Colors.transparent) return 'transparent';
    if (color == Colors.teal) return 'teal';
    if (color == Colors.cyan) return 'cyan';
    if (color == Colors.amber) return 'amber';
    if (color == Colors.indigo) return 'indigo';
    if (color == Colors.lime) return 'lime';

    // Convert to hex string
    final int colorValue = color.toARGB32();
    final r = ((colorValue >> 16) & 0xFF).toRadixString(16).padLeft(2, '0');
    final g = ((colorValue >> 8) & 0xFF).toRadixString(16).padLeft(2, '0');
    final b = (colorValue & 0xFF).toRadixString(16).padLeft(2, '0');
    return '#$r$g$b';
  }

  @override
  State<TypeAheadWidget<T>> createState() => _TypeAheadWidgetState<T>();
}

class _TypeAheadWidgetState<T> extends State<TypeAheadWidget<T>> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  String _searchText = '';
  List<T> _filteredSuggestions = [];
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue);
    _focusNode = widget.focusNode ?? FocusNode();
    _searchText = widget.initialValue;

    _focusNode.addListener(_handleFocusChange);

    // Request focus if autofocus is enabled
    if (widget.autofocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });
    }
  }

  // Handle focus changes
  void _handleFocusChange() {
    final hasFocus = _focusNode.hasFocus;

    if (hasFocus) {
      if (widget.showSuggestionsOnFocus) {
        _filterSuggestions();
        _showOverlay();
      }
    } else {
      // Add a longer delay before hiding overlay to allow tap events to complete
      // This prevents the overlay from disappearing before suggestion selection
      Future.delayed(const Duration(milliseconds: 300), () {
        if (!_focusNode.hasFocus && mounted) {
          _hideOverlay();
        }
      });
    }

    if (widget.onFocus != null) {
      widget.onFocus!(hasFocus);
    }

    setState(() {});
  }

  // Handle hover changes
  void _handleHoverChange(bool isHovered) {
    if (_isHovered != isHovered) {
      setState(() {
        _isHovered = isHovered;
      });

      if (widget.onHover != null) {
        widget.onHover!(isHovered);
      }
    }
  }

  @override
  void didUpdateWidget(TypeAheadWidget<T> oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Check if suggestions have changed
    if (oldWidget.suggestions != widget.suggestions) {
      // Re-filter suggestions with the new list
      _filterSuggestions();

      // If overlay is currently shown, update it with new suggestions
      if (_overlayEntry != null) {
        if (_filteredSuggestions.isNotEmpty) {
          _showOverlay(); // This will recreate the overlay with new suggestions
        } else {
          _hideOverlay(); // Hide if no suggestions match
        }
      }

      setState(() {});
    }

    // Update controller text if initialValue changed
    if (oldWidget.initialValue != widget.initialValue) {
      _controller.text = widget.initialValue;
      _searchText = widget.initialValue;
    }
  }

  @override
  void dispose() {
    _hideOverlay();
    _controller.dispose();

    // Clean up focus node
    if (widget.focusNode == null) {
      // Only dispose the focus node if we created it
      _focusNode.removeListener(_handleFocusChange);
      _focusNode.dispose();
    } else {
      // Just remove our listener
      _focusNode.removeListener(_handleFocusChange);
    }

    super.dispose();
  }

  /// Helper method to extract display text from suggestion objects
  String _getDisplayText(T suggestion) {
    if (widget.displayTextExtractor != null) {
      return widget.displayTextExtractor!(suggestion);
    }
    return suggestion.toString();
  }

  /// Helper method to extract value from suggestion objects
  String _getValue(T suggestion) {
    if (widget.valueExtractor != null) {
      return widget.valueExtractor!(suggestion);
    }
    return suggestion.toString();
  }

  void _filterSuggestions({bool showAll = false}) {
    if (!showAll && _searchText.length < widget.minCharsForSuggestions) {
      _filteredSuggestions = [];
      return;
    }

    if (showAll) {
      // Show all suggestions when dropdown arrow is clicked
      _filteredSuggestions = List.from(widget.suggestions);
    } else {
      final lowerCaseSearch = _searchText.toLowerCase();
      _filteredSuggestions =
          widget.suggestions
              .where(
                (suggestion) => _getDisplayText(
                  suggestion,
                ).toLowerCase().contains(lowerCaseSearch),
              )
              .toList();
    }

    if (widget.sortAlphabetically) {
      _filteredSuggestions.sort(
        (a, b) => _getDisplayText(a).compareTo(_getDisplayText(b)),
      );
    }

    if (_filteredSuggestions.length > widget.maxSuggestions) {
      _filteredSuggestions = _filteredSuggestions.sublist(
        0,
        widget.maxSuggestions,
      );
    }
  }

  void _showOverlay() {
    if (_filteredSuggestions.isEmpty) {
      _hideOverlay();
      return;
    }

    _overlayEntry?.remove();
    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);
  }

  void _hideOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  OverlayEntry _createOverlayEntry() {
    final renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;

    return OverlayEntry(
      builder:
          (context) => Positioned(
            width: size.width,
            child: CompositedTransformFollower(
              link: _layerLink,
              showWhenUnlinked: false,
              offset: Offset(0.0, size.height + 5.0),
              child: Material(
                elevation: 8.0,
                borderRadius: BorderRadius.circular(8.0),
                color: Colors.white,
                child: Container(
                  constraints: BoxConstraints(maxHeight: 200),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8.0),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: ListView.builder(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    shrinkWrap: true,
                    itemCount: _filteredSuggestions.length,
                    itemBuilder: (context, index) {
                      final suggestion = _filteredSuggestions[index];
                      return _buildSuggestionItem(suggestion);
                    },
                  ),
                ),
              ),
            ),
          ),
    );
  }

  Widget _buildSuggestionItem(T suggestion) {
    return _TypeAheadOptionItem<T>(
      suggestion: suggestion,
      displayText: _getDisplayText(suggestion),
      searchText: _searchText,
      fontSize: _getResponsiveFontSize(context),
      highlightMatches: widget.highlightMatches,
      suggestionColor: widget.suggestionColor,
      highlightColor: widget.highlightColor,
      onTap: () => _selectSuggestion(suggestion),
    );
  }

  void _selectSuggestion(T suggestion) {
    final displayText = _getDisplayText(suggestion);
    _controller.text = displayText;
    _searchText = displayText;
    _hideOverlay();
    _focusNode.unfocus();

    if (widget.onSuggestionSelected != null) {
      widget.onSuggestionSelected!(suggestion);
    }

    if (widget.onChanged != null) {
      widget.onChanged!(displayText);
    }

    setState(() {});
  }

  void _onTextChanged(String value) {
    _searchText = value;

    if (widget.onChanged != null) {
      widget.onChanged!(value);
    }

    _filterSuggestions();

    if (_filteredSuggestions.isNotEmpty) {
      _showOverlay();
    } else {
      _hideOverlay();
    }

    setState(() {});
  }

  void _clearText() {
    _controller.clear();
    _searchText = '';
    _hideOverlay();

    if (widget.onChanged != null) {
      widget.onChanged!('');
    }

    setState(() {});
  }

  void _showAllSuggestions() {
    // Focus the field first
    _focusNode.requestFocus();

    // Show all suggestions regardless of search text
    _filterSuggestions(showAll: true);

    if (_filteredSuggestions.isNotEmpty) {
      _showOverlay();
    }

    setState(() {});
  }

  double _getResponsiveHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 56.0; // Extra Large (>1920px)
    } else if (screenWidth >= 1440) {
      return 48.0; // Large (1440-1920px)
    } else if (screenWidth >= 1280) {
      return 40.0; // Medium (1280-1366px)
    } else if (screenWidth >= 768) {
      return 32.0; // Small (768-1024px)
    } else {
      return 32.0; // Default for very small screens
    }
  }

  double _getResponsiveBoxsize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 8.0; // Extra Large (>1920px)
    } else if (screenWidth >= 1440) {
      return 8.0; // Large (1440-1920px)
    } else if (screenWidth >= 1280) {
      return 6.0; // Medium (1280-1366px)
    } else if (screenWidth >= 768) {
      return 4.0; // Small (768-1024px)
    } else {
      return 4.0; // Default for very small screens
    }
  }

  @override
  Widget build(BuildContext context) {
    // Define hover colors matching DropdownWidget
    final hoverBackgroundColor = Colors.white;
    final hoverBorderColor = const Color(0xFF0058FF);
    final hoverIconColor = const Color(0xFF0058FF);
    final defaultBorderColor = const Color(0xFFCCCCCC);
    final defaultIconColor = const Color(0xFFCCCCCC);

    // Create custom input field matching DropdownWidget style
    Widget customInputField = GestureDetector(
      onTap: () {
        _focusNode.requestFocus();
        if (widget.onTap != null) {
          widget.onTap!();
        }
      },
      child: Container(
        height: _getResponsiveHeight(context),
        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
        decoration: BoxDecoration(
          color: _isHovered ? hoverBackgroundColor : Colors.white,
          borderRadius: BorderRadius.circular(4.0),
          border: Border.all(
            color: _isHovered ? hoverBorderColor : defaultBorderColor,
            width: 1.0,
          ),
        ),
        child: Row(
          children: [
            Theme(
              data: Theme.of(context).copyWith(
                hoverColor: Colors.transparent,
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
              ),
              child: Expanded(
                child: TextField(
                  controller: _controller,
                  focusNode: _focusNode,

                  enabled: widget.enabled,
                  autocorrect: widget.autoCorrect,
                  textCapitalization:
                      widget.autoCapitalize
                          ? TextCapitalization.sentences
                          : TextCapitalization.none,
                  // style: TextStyle(
                  //   color: Colors.black87,
                  //   fontSize: _getResponsiveFontSize(context),
                  //   fontWeight: FontWeight.normal,
                  // ),
                  style: FontManager.getCustomStyle(
                    fontFamily: FontManager.fontFamilyInter,
                    fontWeight: FontManager.medium,
                    color: Color(0xFF333333),
                    fontSize: _getResponsiveInputFontSize(context),
                  ),
                  decoration: InputDecoration(
                    hintText: widget.hintText,
                    // hintStyle: TextStyle(
                    //   color: Colors.grey.shade500,
                    //   fontSize: _getResponsiveFontSize(context),
                    // )
                    hintStyle: FontManager.getCustomStyle(
                      fontFamily: FontManager.fontFamilyInter,
                      fontWeight: FontManager.medium,
                      color: Colors.grey.shade500,
                      fontSize: _getResponsiveInputFontSize(context),
                    ),
                    border: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    disabledBorder: InputBorder.none,
                    errorBorder: InputBorder.none,
                    focusedErrorBorder: InputBorder.none,
                    contentPadding: EdgeInsets.zero,
                    isDense: true,
                  ),
                  onChanged: _onTextChanged,
                  onSubmitted: (value) {
                    if (widget.onSubmitted != null) {
                      widget.onSubmitted!(value);
                    }

                    if (!widget.allowCustomValues &&
                        !widget.suggestions.any(
                          (suggestion) => _getDisplayText(suggestion) == value,
                        ) &&
                        _filteredSuggestions.isNotEmpty) {
                      _selectSuggestion(_filteredSuggestions.first);
                    }
                  },
                ),
              ),
            ),
            if (widget.showSuffixButton ?? false)
              GestureDetector(
                onTap:
                    widget.showClearButton && _searchText.isNotEmpty
                        ? _clearText
                        : _showAllSuggestions,
                child: Icon(
                  widget.showClearButton && _searchText.isNotEmpty
                      ? Icons.clear
                      : Icons.arrow_drop_down,
                  color: _isHovered ? hoverIconColor : defaultIconColor,
                  size: _getResponsiveFontSize(context) * 1.5,
                ),
              ),
          ],
        ),
      ),
    );

    // Wrap with mouse region for hover detection
    Widget hoverWidget = MouseRegion(
      onEnter: (_) => _handleHoverChange(true),
      onExit: (_) => _handleHoverChange(false),
      child: customInputField,
    );

    // Wrap with gesture detector for advanced interactions
    Widget gestureWidget = GestureDetector(
      onDoubleTap: widget.onDoubleTap,
      onLongPress: widget.onLongPress,
      child: hoverWidget,
    );

    // Add tooltip if needed
    if (widget.tooltip != null) {
      gestureWidget = Tooltip(message: widget.tooltip!, child: gestureWidget);
    }

    // Add semantics if needed
    if (widget.semanticsLabel != null) {
      gestureWidget = Semantics(
        label: widget.semanticsLabel,
        textField: true,
        child: gestureWidget,
      );
    }

    // Wrap with the transform target for the overlay
    return CompositedTransformTarget(
      link: _layerLink,
      child: SizedBox(
        width: widget.width,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Label/Title display (matching DropdownWidget)
            if (widget.labelText != null)
              Column(
                children: [
                  Text(
                    widget.labelText!,
                    // style: TextStyle(
                    //   fontSize: _getResponsiveFontSize(context),
                    //   fontWeight: FontWeight.w500,
                    //   fontFamily: 'Inter',
                    //   color: Colors.black87,
                    // ),
                    style: FontManager.getCustomStyle(
                      fontFamily: FontManager.fontFamilyInter,
                      fontWeight: FontManager.medium,
                      color: Color(0xFF333333),
                      fontSize: _getResponsiveFontSize(context),
                    ),
                  ),
                  SizedBox(height: _getResponsiveBoxsize(context)),
                ],
              ),

            // Input field
            gestureWidget,

            // Helper text
            if (widget.helperText != null)
              Padding(
                padding: const EdgeInsets.only(top: 4.0),
                child: Text(
                  widget.helperText!,
                  // style: TextStyle(
                  //   color: Colors.grey.shade600,
                  //   fontSize: _getResponsiveFontSize(context) * 0.8,
                  // ),
                  style: FontManager.getCustomStyle(
                    fontFamily: FontManager.fontFamilyInter,
                    fontWeight: FontManager.medium,
                    color: Colors.grey.shade600,
                    fontSize: _getResponsiveFontSize(context),
                  ),
                ),
              ),

            // Error text
            if (widget.errorText != null)
              Padding(
                padding: const EdgeInsets.only(top: 4.0),
                child: Text(
                  widget.errorText!,
                  style: FontManager.getCustomStyle(
                    fontFamily: FontManager.fontFamilyInter,
                    fontWeight: FontManager.medium,
                    color: Colors.red,
                    fontSize: _getResponsiveFontSize(context),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

/// A custom type-ahead option item widget with hover effects and background colors
/// matching the DropdownWidget's _DropdownOptionItem styling
class _TypeAheadOptionItem<T> extends StatefulWidget {
  final T suggestion;
  final String displayText;
  final String searchText;
  final double fontSize;
  final bool highlightMatches;
  final Color? suggestionColor;
  final Color? highlightColor;
  final VoidCallback onTap;

  const _TypeAheadOptionItem({
    required this.suggestion,
    required this.displayText,
    required this.searchText,
    required this.fontSize,
    required this.highlightMatches,
    this.suggestionColor,
    this.highlightColor,
    required this.onTap,
  });

  @override
  State<_TypeAheadOptionItem<T>> createState() =>
      _TypeAheadOptionItemState<T>();
}

class _TypeAheadOptionItemState<T> extends State<_TypeAheadOptionItem<T>> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    // Define background colors for different states (matching DropdownWidget)
    Color backgroundColor;
    if (_isHovered) {
      // Hover state: Light blue background
      backgroundColor = const Color(0xFFf0f5ff);
    } else {
      // Normal state: Transparent background
      backgroundColor = Colors.transparent;
    }

    // Build content with highlighting if needed
    Widget content;
    if (widget.highlightMatches && widget.searchText.isNotEmpty) {
      final lowerCaseSuggestion = widget.displayText.toLowerCase();
      final lowerCaseSearch = widget.searchText.toLowerCase();
      final matchIndex = lowerCaseSuggestion.indexOf(lowerCaseSearch);

      if (matchIndex >= 0) {
        final beforeMatch = widget.displayText.substring(0, matchIndex);
        final matchText = widget.displayText.substring(
          matchIndex,
          matchIndex + widget.searchText.length,
        );
        final afterMatch = widget.displayText.substring(
          matchIndex + widget.searchText.length,
        );

        content = RichText(
          text: TextSpan(
            // style: TextStyle(
            //   color: widget.suggestionColor ?? Colors.black87,
            //   fontSize: widget.fontSize,
            //   fontWeight: FontWeight.normal,
            // ),
            style: FontManager.getCustomStyle(
              fontFamily: FontManager.fontFamilyInter,
              fontWeight: FontManager.medium,
              color: widget.suggestionColor ?? Color(0xFF333333),
              fontSize: _getResponsiveFontSize(context),
            ),
            children: [
              TextSpan(text: beforeMatch),
              TextSpan(
                text: matchText,
                // style: TextStyle(
                //   fontWeight: FontWeight.w500,
                //   color: widget.highlightColor ?? const Color(0xFF0058FF),
                // ),
                style: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.medium,
                  color: widget.highlightColor ?? const Color(0xFF0058FF),
                  fontSize: _getResponsiveFontSize(context),
                ),
              ),
              TextSpan(text: afterMatch),
            ],
          ),
        );
      } else {
        content = Text(
          widget.displayText,
          // style: TextStyle(
          //   color: widget.suggestionColor ?? Colors.black87,
          //   fontSize: widget.fontSize,
          //   fontWeight: FontWeight.normal,
          // ),
          style: FontManager.getCustomStyle(
            fontFamily: FontManager.fontFamilyInter,
            fontWeight: FontManager.medium,
            color: widget.suggestionColor ?? Color(0xFF333333),
            fontSize: _getResponsiveFontSize(context),
          ),
        );
      }
    } else {
      content = Text(
        widget.displayText,
        // style: TextStyle(
        //   color: widget.suggestionColor ?? Colors.black87,
        //   fontSize: widget.fontSize,
        //   fontWeight: FontWeight.normal,
        // ),
        style: FontManager.getCustomStyle(
          fontFamily: FontManager.fontFamilyInter,
          fontWeight: FontManager.medium,
          color: widget.suggestionColor ?? Color(0xFF333333),
          fontSize: _getResponsiveFontSize(context),
        ),
      );
    }

    return MouseRegion(
      onEnter: (_) {
        setState(() {
          _isHovered = true;
        });
      },
      onExit: (_) {
        setState(() {
          _isHovered = false;
        });
      },
      child: InkWell(
        onTap: widget.onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(color: backgroundColor),
          child: content,
        ),
      ),
    );
  }
}

double _getResponsiveInputFontSize(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 16.0; // Extra Large (>1920px) - Reduced for better fit
  } else if (screenWidth >= 1440) {
    return 15.0; // Large (1440-1920px) - Reduced for better fit
  } else if (screenWidth >= 1280) {
    return 14.0; // Medium (1280-1366px) - Standard size
  } else if (screenWidth >= 768) {
    return 14.0; // Small (768-1024px) - Increased for readability
  } else {
    return 14.0; // Default for very small screens - Consistent
  }
}

double _getResponsiveFontSize(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 18.0; // Extra Large
  } else if (screenWidth >= 1440) {
    return 16.0; // Large
  } else if (screenWidth >= 1280) {
    return 14.0; // Medium
  } else {
    return 12.0; // Default for very small screens
  }
}
