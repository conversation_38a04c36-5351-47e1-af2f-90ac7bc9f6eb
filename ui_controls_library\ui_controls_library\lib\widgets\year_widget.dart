import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';
import 'package:ui_controls_library/widgets/utils/font_manager.dart';

/// A customizable widget for selecting a year.
///
/// This widget provides a rich set of customization options for year selection,
/// including various display formats, styling options, and interaction capabilities.
class YearWidget extends StatefulWidget {
  /// The initial year value.
  final int? initialYear;

  /// The minimum selectable year.
  final int? minYear;

  /// The maximum selectable year.
  final int? maxYear;

  /// The format for displaying the selected year.
  final String format;

  /// The locale for formatting (e.g., 'en_US', 'fr_FR').
  final String locale;

  /// The text color.
  final Color textColor;

  /// The background color.
  final Color backgroundColor;

  /// The border color.
  final Color borderColor;

  /// The border width.
  final double borderWidth;

  /// The border radius.
  final double borderRadius;

  /// Whether to show a border.
  final bool hasBorder;

  /// Whether the widget is read-only.
  final bool isReadOnly;

  /// Whether the widget is disabled.
  final bool isDisabled;

  /// The label text.
  final String? label;

  /// The hint text.
  final String? hint;

  /// The helper text.
  final String? helperText;

  /// The error text.
  final String? errorText;

  /// Whether to show a prefix.
  final bool showPrefix;

  /// Whether to show a suffix.
  final bool showSuffix;

  /// The prefix icon.
  final IconData? prefixIcon;

  /// The suffix icon.
  final IconData? suffixIcon;

  /// The font size.
  final double fontSize;

  /// The font weight.
  final FontWeight fontWeight;

  /// Whether the widget is compact.
  final bool isCompact;

  /// The text alignment.
  final TextAlign textAlign;

  /// Whether to show a shadow.
  final bool hasShadow;

  /// The elevation of the shadow.
  final double elevation;

  /// Whether to use dark theme.
  final bool isDarkTheme;

  /// Whether to show animation.
  final bool hasAnimation;

  /// The prefix text.
  final String? prefixText;

  /// The suffix text.
  final String? suffixText;

  /// Whether to show a year dropdown.
  final bool showYearDropdown;

  /// Whether to show year navigation arrows.
  final bool showYearArrows;

  /// Whether to show a calendar icon.
  final bool showCalendarIcon;

  /// Whether to show a clear button.
  final bool showClearButton;

  /// Whether to allow future dates.
  final bool allowFutureDates;

  /// Whether to allow past dates.
  final bool allowPastDates;

  /// Whether to default to current year if not specified.
  final bool defaultToCurrent;

  /// The display mode for the widget.
  final YearDisplayMode displayMode;

  /// The callback when the year changes.
  final Function(int year)? onChanged;

  /// The padding around the widget.
  final EdgeInsetsGeometry padding;

  /// The width of the widget.
  final double? width;

  /// The height of the widget.
  final double? height;

  /// The step size for year navigation.
  final int yearStep;

  /// Whether to show the year as a 2-digit number.
  final bool showShortYear;

  /// Whether to show the era (BC/AD).
  final bool showEra;

  /// The era format.
  final String eraFormat;

  // Advanced Interaction Properties

  /// Callback for mouse hover
  ///
  /// This function is called when the mouse pointer enters or exits the widget.
  /// The parameter is true when the mouse enters and false when it exits.
  final void Function(bool)? onHover;

  /// Callback for keyboard focus
  ///
  /// This function is called when the widget gains or loses keyboard focus.
  /// The parameter is true when focus is gained and false when it is lost.
  final void Function(bool)? onFocus;

  /// Focus node for manual focus control
  ///
  /// This allows for programmatic control of the widget's focus state.
  /// If null, the widget will create and manage its own focus node.
  final FocusNode? focusNode;

  /// Whether the widget should be focused automatically when it appears
  ///
  /// If true, the widget will request focus when it is first built.
  final bool autofocus;

  /// Color when the widget is hovered
  final Color? hoverColor;

  /// Color when the widget is focused
  final Color? focusColor;

  /// Tooltip text to display on hover
  final String? tooltip;

  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Callback for tap gesture
  ///
  /// This function is called when the widget is tapped.
  final VoidCallback? onTap;

  /// Callback for double tap gesture
  ///
  /// This function is called when the widget is double-tapped.
  final VoidCallback? onDoubleTap;

  /// Callback for long press gesture
  ///
  /// This function is called when the widget is long-pressed.
  final VoidCallback? onLongPress;

  /// More general gesture callback
  ///
  /// This function is called when the widget is tapped.
  final GestureTapCallback? gestureTapCallback;

  /// Whether to enable haptic/audio feedback
  ///
  /// If true, the widget will provide haptic and/or audio feedback when interacted with.
  final bool enableFeedback;

  /// Creates a year widget.
  const YearWidget({
    super.key,
    this.initialYear,
    this.minYear,
    this.maxYear,
    this.format = 'y',
    this.locale = 'en_US',
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.borderColor = const Color(0xFFCCCCCC),
    this.borderWidth = 1.0,
    this.borderRadius = 4.0,
    this.hasBorder = true,
    this.isReadOnly = false,
    this.isDisabled = false,
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.showPrefix = false,
    this.showSuffix = false,
    this.prefixIcon,
    this.suffixIcon,
    this.fontSize = 16.0,
    this.fontWeight = FontManager.medium,
    this.isCompact = false,
    this.textAlign = TextAlign.start,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.isDarkTheme = false,
    this.hasAnimation = false,
    this.prefixText,
    this.suffixText,
    this.showYearDropdown = true,
    this.showYearArrows = false,
    this.showCalendarIcon = true,
    this.showClearButton = false,
    this.allowFutureDates = true,
    this.allowPastDates = true,
    this.defaultToCurrent = true,
    this.displayMode = YearDisplayMode.dropdown,
    this.onChanged,
    this.padding = const EdgeInsets.all(12.0),
    this.width,
    this.height,
    this.yearStep = 1,
    this.showShortYear = false,
    this.showEra = false,
    this.eraFormat = 'G',
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.autofocus = false,
    this.hoverColor,
    this.focusColor,
    this.tooltip,
    this.semanticsLabel,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    this.gestureTapCallback,
    this.enableFeedback = true,
  });

  /// Creates a YearWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the YearWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "initialYear": 2023,
  ///   "minYear": 1900,
  ///   "maxYear": 2100,
  ///   "format": "y",
  ///   "textColor": "black",
  ///   "backgroundColor": "white",
  ///   "displayMode": "dropdown"
  /// }
  /// ```
  factory YearWidget.fromJson(Map<String, dynamic> json) {
    // Parse display mode
    YearDisplayMode displayMode = YearDisplayMode.inline;
    if (json['displayMode'] != null) {
      switch (json['displayMode'].toString().toLowerCase()) {
        case 'inline':
          displayMode = YearDisplayMode.inline;
          break;
        case 'dropdown':
          displayMode = YearDisplayMode.dropdown;
          break;
        case 'dialog':
          displayMode = YearDisplayMode.dialog;
          break;
        case 'bottomsheet':
        case 'bottom_sheet':
          displayMode = YearDisplayMode.bottomSheet;
          break;
        case 'chip':
          displayMode = YearDisplayMode.chip;
          break;
        case 'slider':
          displayMode = YearDisplayMode.slider;
          break;
        case 'numberpicker':
        case 'number_picker':
          displayMode = YearDisplayMode.numberPicker;
          break;
      }
    }

    // Parse text align
    TextAlign textAlign = TextAlign.start;
    if (json['textAlign'] != null) {
      switch (json['textAlign'].toString().toLowerCase()) {
        case 'center':
          textAlign = TextAlign.center;
          break;
        case 'end':
        case 'right':
          textAlign = TextAlign.end;
          break;
        case 'start':
        case 'left':
          textAlign = TextAlign.start;
          break;
        case 'justify':
          textAlign = TextAlign.justify;
          break;
      }
    }

    // Parse font weight
    FontWeight fontWeight = FontWeight.normal;
    if (json['fontWeight'] != null) {
      final weight = json['fontWeight'];
      if (weight is int) {
        switch (weight) {
          case 100:
            fontWeight = FontWeight.w100;
            break;
          case 200:
            fontWeight = FontWeight.w200;
            break;
          case 300:
            fontWeight = FontWeight.w300;
            break;
          case 400:
            fontWeight = FontWeight.normal;
            break;
          case 500:
            fontWeight = FontWeight.w500;
            break;
          case 600:
            fontWeight = FontWeight.w600;
            break;
          case 700:
            fontWeight = FontWeight.bold;
            break;
          case 800:
            fontWeight = FontWeight.w800;
            break;
          case 900:
            fontWeight = FontWeight.w900;
            break;
        }
      } else if (weight is String) {
        switch (weight.toLowerCase()) {
          case 'thin':
            fontWeight = FontWeight.w100;
            break;
          case 'extralight':
          case 'extra_light':
            fontWeight = FontWeight.w200;
            break;
          case 'light':
            fontWeight = FontWeight.w300;
            break;
          case 'normal':
          case 'regular':
            fontWeight = FontWeight.normal;
            break;
          case 'medium':
            fontWeight = FontWeight.w500;
            break;
          case 'semibold':
          case 'semi_bold':
            fontWeight = FontWeight.w600;
            break;
          case 'bold':
            fontWeight = FontWeight.bold;
            break;
          case 'extrabold':
          case 'extra_bold':
            fontWeight = FontWeight.w800;
            break;
          case 'black':
            fontWeight = FontWeight.w900;
            break;
        }
      }
    }

    // Parse colors
    Color? parseColor(dynamic colorValue) {
      if (colorValue == null) return null;

      if (colorValue is String) {
        // Handle named colors
        switch (colorValue.toLowerCase()) {
          case 'red':
            return Colors.red;
          case 'blue':
            return Colors.blue;
          case 'green':
            return Colors.green;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'brown':
            return Colors.brown;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          case 'transparent':
            return Colors.transparent;
          default:
            // Handle hex colors
            if (colorValue.startsWith('#')) {
              try {
                final hexColor = colorValue.substring(1);
                final hexValue = int.parse(
                  '0xFF${hexColor.padRight(8, 'F').substring(0, 8)}',
                );
                return Color(hexValue);
              } catch (e) {
                return null;
              }
            }
            return null;
        }
      } else if (colorValue is int) {
        return Color(colorValue);
      }

      return null;
    }

    // Parse padding
    EdgeInsetsGeometry parsePadding(dynamic paddingValue) {
      if (paddingValue == null) return const EdgeInsets.all(12.0);

      if (paddingValue is num) {
        return EdgeInsets.all(paddingValue.toDouble());
      } else if (paddingValue is Map<String, dynamic>) {
        final left = (paddingValue['left'] as num?)?.toDouble() ?? 0.0;
        final top = (paddingValue['top'] as num?)?.toDouble() ?? 0.0;
        final right = (paddingValue['right'] as num?)?.toDouble() ?? 0.0;
        final bottom = (paddingValue['bottom'] as num?)?.toDouble() ?? 0.0;

        if (paddingValue.containsKey('horizontal') ||
            paddingValue.containsKey('vertical')) {
          final horizontal =
              (paddingValue['horizontal'] as num?)?.toDouble() ?? 0.0;
          final vertical =
              (paddingValue['vertical'] as num?)?.toDouble() ?? 0.0;
          return EdgeInsets.symmetric(
            horizontal: horizontal,
            vertical: vertical,
          );
        }

        return EdgeInsets.fromLTRB(left, top, right, bottom);
      } else if (paddingValue is String) {
        switch (paddingValue.toLowerCase()) {
          case 'none':
          case 'zero':
            return EdgeInsets.zero;
          case 'small':
            return const EdgeInsets.all(4.0);
          case 'medium':
            return const EdgeInsets.all(8.0);
          case 'large':
            return const EdgeInsets.all(16.0);
          case 'xlarge':
            return const EdgeInsets.all(24.0);
          default:
            return const EdgeInsets.all(12.0);
        }
      }

      return const EdgeInsets.all(12.0);
    }

    // Parse icon data
    IconData? parseIconData(String? iconName) {
      if (iconName == null) return null;

      switch (iconName.toLowerCase()) {
        case 'calendar':
          return Icons.calendar_today;
        case 'date':
          return Icons.date_range;
        case 'time':
          return Icons.access_time;
        case 'clock':
          return Icons.watch_later;
        case 'year':
          return Icons.calendar_view_month;
        case 'month':
          return Icons.calendar_view_month;
        case 'day':
          return Icons.today;
        case 'clear':
          return Icons.clear;
        case 'add':
          return Icons.add;
        case 'remove':
          return Icons.remove;
        case 'edit':
          return Icons.edit;
        case 'save':
          return Icons.save;
        case 'delete':
          return Icons.delete;
        case 'check':
          return Icons.check;
        case 'close':
          return Icons.close;
        case 'arrow_up':
          return Icons.arrow_upward;
        case 'arrow_down':
          return Icons.arrow_downward;
        case 'arrow_left':
          return Icons.arrow_back;
        case 'arrow_right':
          return Icons.arrow_forward;
        case 'arrow_drop_down':
          return Icons.arrow_drop_down;
        case 'arrow_drop_up':
          return Icons.arrow_drop_up;
        default:
          return null;
      }
    }

    return YearWidget(
      initialYear: json['initialYear'] as int?,
      minYear: json['minYear'] as int?,
      maxYear: json['maxYear'] as int?,
      format: json['format'] as String? ?? 'y',
      locale: json['locale'] as String? ?? 'en_US',
      textColor: parseColor(json['textColor']) ?? Colors.black,
      backgroundColor: parseColor(json['backgroundColor']) ?? Colors.white,
      borderColor: parseColor(json['borderColor']) ?? const Color(0xFFCCCCCC),
      borderWidth: (json['borderWidth'] as num?)?.toDouble() ?? 1.0,
      borderRadius: (json['borderRadius'] as num?)?.toDouble() ?? 4.0,
      hasBorder: json['hasBorder'] as bool? ?? true,
      isReadOnly: json['isReadOnly'] as bool? ?? false,
      isDisabled: json['isDisabled'] as bool? ?? false,
      label: json['label'] as String?,
      hint: json['hint'] as String?,
      helperText: json['helperText'] as String?,
      errorText: json['errorText'] as String?,
      showPrefix: json['showPrefix'] as bool? ?? false,
      showSuffix: json['showSuffix'] as bool? ?? false,
      prefixIcon: parseIconData(json['prefixIcon'] as String?),
      suffixIcon: parseIconData(json['suffixIcon'] as String?),
      fontSize: (json['fontSize'] as num?)?.toDouble() ?? 16.0,
      fontWeight: fontWeight,
      isCompact: json['isCompact'] as bool? ?? false,
      textAlign: textAlign,
      hasShadow: json['hasShadow'] as bool? ?? false,
      elevation: (json['elevation'] as num?)?.toDouble() ?? 2.0,
      isDarkTheme: json['isDarkTheme'] as bool? ?? false,
      hasAnimation: json['hasAnimation'] as bool? ?? false,
      prefixText: json['prefixText'] as String?,
      suffixText: json['suffixText'] as String?,
      showYearDropdown: json['showYearDropdown'] as bool? ?? true,
      showYearArrows: json['showYearArrows'] as bool? ?? false,
      showCalendarIcon: json['showCalendarIcon'] as bool? ?? true,
      showClearButton: json['showClearButton'] as bool? ?? false,
      allowFutureDates: json['allowFutureDates'] as bool? ?? true,
      allowPastDates: json['allowPastDates'] as bool? ?? true,
      defaultToCurrent: json['defaultToCurrent'] as bool? ?? true,
      displayMode: displayMode,
      onChanged:
          json['onChanged'] == true
              ? (year) {
                debugPrint('Year changed: $year');
              }
              : null,
      padding: parsePadding(json['padding']),
      width: (json['width'] as num?)?.toDouble(),
      height: (json['height'] as num?)?.toDouble(),
      yearStep: json['yearStep'] as int? ?? 1,
      showShortYear: json['showShortYear'] as bool? ?? false,
      showEra: json['showEra'] as bool? ?? false,
      eraFormat: json['eraFormat'] as String? ?? 'G',
      onHover:
          json['onHover'] == true
              ? (isHovered) {
                debugPrint('Year widget hover: $isHovered');
              }
              : null,
      onFocus:
          json['onFocus'] == true
              ? (isFocused) {
                debugPrint('Year widget focus: $isFocused');
              }
              : null,
      focusNode: null, // Cannot be created from JSON
      autofocus: json['autofocus'] as bool? ?? false,
      hoverColor: parseColor(json['hoverColor']),
      focusColor: parseColor(json['focusColor']),
      tooltip: json['tooltip'] as String?,
      semanticsLabel: json['semanticsLabel'] as String?,
      onTap:
          json['onTap'] == true
              ? () {
                debugPrint('Year widget tapped');
              }
              : null,
      onDoubleTap:
          json['onDoubleTap'] == true
              ? () {
                debugPrint('Year widget double-tapped');
              }
              : null,
      onLongPress:
          json['onLongPress'] == true
              ? () {
                debugPrint('Year widget long-pressed');
              }
              : null,
      gestureTapCallback:
          json['gestureTapCallback'] == true
              ? () {
                debugPrint('Year widget gesture tap');
              }
              : null,
      enableFeedback: json['enableFeedback'] as bool? ?? true,
    );
  }

  /// Converts the YearWidget to a JSON map
  ///
  /// This method allows for serializing the widget configuration to JSON,
  /// which can be useful for saving configurations or sharing them.
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {
      'format': format,
      'locale': locale,
      'textColor': _colorToString(textColor),
      'backgroundColor': _colorToString(backgroundColor),
      'hasBorder': hasBorder,
      'isReadOnly': isReadOnly,
      'isDisabled': isDisabled,
      'showPrefix': showPrefix,
      'showSuffix': showSuffix,
      'fontSize': fontSize,
      'fontWeight': _fontWeightToString(fontWeight),
      'isCompact': isCompact,
      'textAlign': _textAlignToString(textAlign),
      'hasShadow': hasShadow,
      'elevation': elevation,
      'isDarkTheme': isDarkTheme,
      'hasAnimation': hasAnimation,
      'showYearDropdown': showYearDropdown,
      'showYearArrows': showYearArrows,
      'showCalendarIcon': showCalendarIcon,
      'showClearButton': showClearButton,
      'allowFutureDates': allowFutureDates,
      'allowPastDates': allowPastDates,
      'defaultToCurrent': defaultToCurrent,
      'displayMode': _displayModeToString(displayMode),
      'yearStep': yearStep,
      'showShortYear': showShortYear,
      'showEra': showEra,
      'eraFormat': eraFormat,
      'autofocus': autofocus,
      'enableFeedback': enableFeedback,
    };

    // Add optional properties
    if (initialYear != null) json['initialYear'] = initialYear;
    if (minYear != null) json['minYear'] = minYear;
    if (maxYear != null) json['maxYear'] = maxYear;
    if (borderColor != Colors.grey)
      json['borderColor'] = _colorToString(borderColor);
    if (borderWidth != 1.0) json['borderWidth'] = borderWidth;
    if (borderRadius != 4.0) json['borderRadius'] = borderRadius;
    if (label != null) json['label'] = label;
    if (hint != null) json['hint'] = hint;
    if (helperText != null) json['helperText'] = helperText;
    if (errorText != null) json['errorText'] = errorText;
    if (prefixIcon != null) json['prefixIcon'] = _iconDataToString(prefixIcon);
    if (suffixIcon != null) json['suffixIcon'] = _iconDataToString(suffixIcon);
    if (prefixText != null) json['prefixText'] = prefixText;
    if (suffixText != null) json['suffixText'] = suffixText;
    if (width != null) json['width'] = width;
    if (height != null) json['height'] = height;
    if (hoverColor != null) json['hoverColor'] = _colorToString(hoverColor!);
    if (focusColor != null) json['focusColor'] = _colorToString(focusColor!);
    if (tooltip != null) json['tooltip'] = tooltip;
    if (semanticsLabel != null) json['semanticsLabel'] = semanticsLabel;

    // Add callback flags
    if (onChanged != null) json['onChanged'] = true;
    if (onHover != null) json['onHover'] = true;
    if (onFocus != null) json['onFocus'] = true;
    if (onTap != null) json['onTap'] = true;
    if (onDoubleTap != null) json['onDoubleTap'] = true;
    if (onLongPress != null) json['onLongPress'] = true;
    if (gestureTapCallback != null) json['gestureTapCallback'] = true;

    return json;
  }

  /// Helper method to convert a Color to a string
  static String _colorToString(Color color) {
    // Handle standard colors by name for better readability
    if (color == Colors.red) return 'red';
    if (color == Colors.blue) return 'blue';
    if (color == Colors.green) return 'green';
    if (color == Colors.yellow) return 'yellow';
    if (color == Colors.orange) return 'orange';
    if (color == Colors.purple) return 'purple';
    if (color == Colors.pink) return 'pink';
    if (color == Colors.brown) return 'brown';
    if (color == Colors.grey) return 'grey';
    if (color == Colors.black) return 'black';
    if (color == Colors.white) return 'white';
    if (color == Colors.transparent) return 'transparent';

    // Convert to hex string
    final int colorValue = color.toARGB32();
    final r = ((colorValue >> 16) & 0xFF).toRadixString(16).padLeft(2, '0');
    final g = ((colorValue >> 8) & 0xFF).toRadixString(16).padLeft(2, '0');
    final b = (colorValue & 0xFF).toRadixString(16).padLeft(2, '0');
    return '#$r$g$b';
  }

  /// Helper method to convert a FontWeight to a string
  static String _fontWeightToString(FontWeight weight) {
    if (weight == FontWeight.bold) return 'bold';
    if (weight == FontWeight.normal) return 'normal';
    if (weight == FontWeight.w100) return 'thin';
    if (weight == FontWeight.w200) return 'extralight';
    if (weight == FontWeight.w300) return 'light';
    if (weight == FontWeight.w500) return 'medium';
    if (weight == FontWeight.w600) return 'semibold';
    if (weight == FontWeight.w800) return 'extrabold';
    if (weight == FontWeight.w900) return 'black';

    return 'normal';
  }

  /// Helper method to convert a TextAlign to a string
  static String _textAlignToString(TextAlign align) {
    if (align == TextAlign.center) return 'center';
    if (align == TextAlign.end) return 'end';
    if (align == TextAlign.justify) return 'justify';

    return 'start';
  }

  /// Helper method to convert a YearDisplayMode to a string
  static String _displayModeToString(YearDisplayMode mode) {
    switch (mode) {
      case YearDisplayMode.inline:
        return 'inline';
      case YearDisplayMode.dropdown:
        return 'dropdown';
      case YearDisplayMode.dialog:
        return 'dialog';
      case YearDisplayMode.bottomSheet:
        return 'bottomsheet';
      case YearDisplayMode.chip:
        return 'chip';
      case YearDisplayMode.slider:
        return 'slider';
      case YearDisplayMode.numberPicker:
        return 'numberpicker';
    }
  }

  /// Helper method to convert an IconData to a string
  static String? _iconDataToString(IconData? icon) {
    if (icon == null) return null;

    if (icon == Icons.calendar_today) return 'calendar';
    if (icon == Icons.date_range) return 'date';
    if (icon == Icons.access_time) return 'time';
    if (icon == Icons.watch_later) return 'clock';
    if (icon == Icons.calendar_view_month) return 'month';
    if (icon == Icons.today) return 'day';
    if (icon == Icons.clear) return 'clear';
    if (icon == Icons.add) return 'add';
    if (icon == Icons.remove) return 'remove';
    if (icon == Icons.edit) return 'edit';
    if (icon == Icons.save) return 'save';
    if (icon == Icons.delete) return 'delete';
    if (icon == Icons.check) return 'check';
    if (icon == Icons.close) return 'close';
    if (icon == Icons.arrow_upward) return 'arrow_up';
    if (icon == Icons.arrow_downward) return 'arrow_down';
    if (icon == Icons.arrow_back) return 'arrow_left';
    if (icon == Icons.arrow_forward) return 'arrow_right';
    if (icon == Icons.arrow_drop_down) return 'arrow_drop_down';
    if (icon == Icons.arrow_drop_up) return 'arrow_drop_up';

    return null;
  }

  @override
  State<YearWidget> createState() => _YearWidgetState();
}

/// The display mode for the year widget.
enum YearDisplayMode {
  /// Show year inline.
  inline,

  /// Show as a dropdown.
  dropdown,

  /// Show as a dialog.
  dialog,

  /// Show as a bottom sheet.
  bottomSheet,

  /// Show as a compact chip.
  chip,

  /// Show as a slider.
  slider,

  /// Show as a number picker.
  numberPicker,
}

class _YearWidgetState extends State<YearWidget>
    with SingleTickerProviderStateMixin {
  late int _selectedYear;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isOpen = false;
  bool _isHovered = false;
  bool _isFocused = false;
  late FocusNode _focusNode;
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;

  @override
  void initState() {
    super.initState();

    // Initialize with current date if defaultToCurrent is true
    final now = DateTime.now();

    if (widget.defaultToCurrent) {
      _selectedYear = widget.initialYear ?? now.year;
    } else {
      _selectedYear = widget.initialYear ?? 2023;
    }

    // Apply min/max year constraints
    if (widget.minYear != null && _selectedYear < widget.minYear!) {
      _selectedYear = widget.minYear!;
    }
    if (widget.maxYear != null && _selectedYear > widget.maxYear!) {
      _selectedYear = widget.maxYear!;
    }

    // Initialize animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.05).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    if (widget.hasAnimation) {
      _animationController.repeat(reverse: true);
    }

    // Initialize focus node
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_handleFocusChange);

    // Request focus if autofocus is enabled
    if (widget.autofocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });
    }
  }

  // Handle focus changes
  void _handleFocusChange() {
    if (_focusNode.hasFocus != _isFocused) {
      setState(() {
        _isFocused = _focusNode.hasFocus;
      });

      if (widget.onFocus != null) {
        widget.onFocus!(_isFocused);
      }
    }
  }

  // Handle hover changes
  void _handleHoverChange(bool isHovered) {
    if (_isHovered != isHovered) {
      setState(() {
        _isHovered = isHovered;
      });

      if (widget.onHover != null) {
        widget.onHover!(isHovered);
      }
    }
  }

  @override
  void dispose() {
    // Clean up overlay
    _removeYearPickerOverlay();

    // Clean up focus node
    if (widget.focusNode == null) {
      // Only dispose the focus node if we created it
      _focusNode.removeListener(_handleFocusChange);
      _focusNode.dispose();
    }

    _animationController.dispose();
    super.dispose();
  }

  void _incrementYear() {
    if (widget.isDisabled || widget.isReadOnly) return;

    setState(() {
      _selectedYear += widget.yearStep;
      if (widget.maxYear != null && _selectedYear > widget.maxYear!) {
        _selectedYear = widget.maxYear!;
      }
    });

    _notifyChange();
  }

  void _decrementYear() {
    if (widget.isDisabled || widget.isReadOnly) return;

    setState(() {
      _selectedYear -= widget.yearStep;
      if (widget.minYear != null && _selectedYear < widget.minYear!) {
        _selectedYear = widget.minYear!;
      }
    });

    _notifyChange();
  }

  void _notifyChange() {
    if (widget.onChanged != null) {
      widget.onChanged!(_selectedYear);
    }
  }

  void _clearSelection() {
    if (widget.isDisabled || widget.isReadOnly) return;

    setState(() {
      final now = DateTime.now();
      _selectedYear =
          widget.defaultToCurrent ? now.year : (widget.minYear ?? 2023);
    });

    _notifyChange();
  }

  String _getFormattedYear() {
    String yearStr;

    if (widget.showShortYear) {
      // Show 2-digit year
      yearStr = (_selectedYear % 100).toString().padLeft(2, '0');
    } else {
      // Use the specified format
      try {
        final formatter = DateFormat(widget.format, widget.locale);
        yearStr = formatter.format(DateTime(_selectedYear));
      } catch (e) {
        // Fallback to simple year display
        yearStr = _selectedYear.toString();
      }
    }

    // Add era if needed
    if (widget.showEra) {
      try {
        final eraFormatter = DateFormat(widget.eraFormat, widget.locale);
        final era = eraFormatter.format(DateTime(_selectedYear));
        yearStr = '$yearStr $era';
      } catch (e) {
        // Ignore era if format fails
      }
    }

    return yearStr;
  }

  void _showYearPicker() {
    if (widget.isDisabled || widget.isReadOnly) return;

    if (!_isOpen) {
      if (widget.displayMode == YearDisplayMode.dropdown) {
        _showYearPickerOverlay();
      } else if (widget.displayMode == YearDisplayMode.dialog) {
        showDialog(
          context: context,
          builder: (context) => _buildYearPickerDialog(),
        ).then((_) {
          setState(() {
            _isOpen = false;
          });
        });
      } else if (widget.displayMode == YearDisplayMode.bottomSheet) {
        showModalBottomSheet(
          context: context,
          builder: (context) => _buildYearPickerSheet(),
        ).then((_) {
          setState(() {
            _isOpen = false;
          });
        });
      }
    } else {
      _removeYearPickerOverlay();
    }

    setState(() {
      _isOpen = !_isOpen;
    });
  }

  void _showYearPickerOverlay() {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;
    final offset = renderBox.localToGlobal(Offset.zero);
    final screenSize = MediaQuery.of(context).size;

    // Calculate available space above and below the widget
    final double spaceBelow = screenSize.height - (offset.dy + size.height);
    final double spaceAbove = offset.dy;

    // Desired popover height (with some padding for safety)
    const double popoverHeight = 350.0;
    const double padding = 20.0; // Extra padding for safety

    // Determine if we should show above or below
    bool showAbove = false;
    double finalPopoverHeight = popoverHeight;

    if (spaceBelow < (popoverHeight + padding)) {
      // Not enough space below
      if (spaceAbove > spaceBelow && spaceAbove > (popoverHeight + padding)) {
        // More space above and enough space, show above
        showAbove = true;
      } else if (spaceAbove > spaceBelow) {
        // More space above but not enough, show above with reduced height
        showAbove = true;
        finalPopoverHeight = spaceAbove - padding;
      } else {
        // More space below, show below with reduced height
        finalPopoverHeight = spaceBelow - padding;
      }
    }

    // Calculate the offset based on positioning
    final Offset popoverOffset =
        showAbove
            ? Offset(0.0, -(finalPopoverHeight + 4)) // Position above with gap
            : Offset(0.0, size.height + 4); // Position below with gap

    _overlayEntry = OverlayEntry(
      builder:
          (context) => Stack(
            children: [
              // This GestureDetector closes the dropdown when tapping outside
              Positioned.fill(
                child: GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    setState(() {
                      _isOpen = false;
                      _removeYearPickerOverlay();
                    });
                  },
                ),
              ),
              Positioned(
                left: offset.dx,
                top: offset.dy,
                width: widget.width ?? size.width,
                child: CompositedTransformFollower(
                  link: _layerLink,
                  showWhenUnlinked: false,
                  offset: popoverOffset,
                  child: Material(
                    elevation: 8.0,
                    borderRadius: BorderRadius.circular(4),
                    color: Colors.white,
                    child: Container(
                      constraints: BoxConstraints(
                        maxHeight: finalPopoverHeight,
                        minHeight: 200, // Minimum height to ensure usability
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(18.0),
                        child: _buildYearPickerContent(),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _removeYearPickerOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  Widget _buildYearPickerDialog() {
    return AlertDialog(
      title: Text('Select Year'),
      content: _buildYearPickerContent(),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          child: Text('Cancel'),
        ),
        TextButton(
          onPressed: () {
            _notifyChange();
            Navigator.of(context).pop();
          },
          child: Text('OK'),
        ),
      ],
    );
  }

  Widget _buildYearPickerSheet() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Select Year',
            // style: TextStyle(fontSize: 18.0, fontWeight: widget.fontWeight),
            style: FontManager.getCustomStyle(
              fontFamily: FontManager.fontFamilyInter,
              fontWeight: FontManager.bold,
              fontSize: 18,
            ),
          ),
          const SizedBox(height: 16.0),
          _buildYearPickerContent(),
          const SizedBox(height: 16.0),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  _notifyChange();
                  Navigator.of(context).pop();
                },
                child: Text('OK'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildYearPickerContent() {
    if (widget.displayMode == YearDisplayMode.slider) {
      return _buildYearSlider();
    } else if (widget.displayMode == YearDisplayMode.numberPicker) {
      return _buildYearNumberPicker();
    } else {
      return _buildYearGrid();
    }
  }

  Widget _buildYearSlider() {
    final minYear = widget.minYear ?? 1900;
    final maxYear = widget.maxYear ?? 2100;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          _selectedYear.toString(),
          // style: TextStyle(
          //   fontSize: widget.fontSize * 1.5,
          //   fontWeight: widget.fontWeight,
          // ),
          style: FontManager.getCustomStyle(
            fontFamily: FontManager.fontFamilyInter,
            fontWeight: FontManager.medium,
            fontSize: _getResponsiveFontSize(context),
          ),
        ),
        const SizedBox(height: 16.0),
        Slider(
          value: _selectedYear.toDouble(),
          min: minYear.toDouble(),
          max: maxYear.toDouble(),
          divisions: maxYear - minYear,
          label: _selectedYear.toString(),
          onChanged:
              widget.isDisabled || widget.isReadOnly
                  ? null
                  : (double value) {
                    setState(() {
                      _selectedYear = value.round();
                    });
                  },
          onChangeEnd: (double value) {
            _notifyChange();
          },
        ),
      ],
    );
  }

  Widget _buildYearNumberPicker() {
    final minYear = widget.minYear ?? 1900;
    final maxYear = widget.maxYear ?? 2100;
    final years = List<int>.generate(maxYear - minYear + 1, (i) => minYear + i);

    return SizedBox(
      height: 200,
      width: 100,
      child: ListView.builder(
        itemCount: years.length,
        itemBuilder: (context, index) {
          final year = years[index];
          final isSelected = year == _selectedYear;

          return GestureDetector(
            onTap:
                widget.isDisabled || widget.isReadOnly
                    ? null
                    : () {
                      setState(() {
                        _selectedYear = year;
                      });
                      _notifyChange();
                    },
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              decoration: BoxDecoration(
                color:
                    isSelected
                        ? Theme.of(context).primaryColor.withAlpha(51)
                        : null, // Alpha 51 is approximately 0.2 opacity
                borderRadius: BorderRadius.circular(4.0),
              ),
              child: Center(
                child: Text(
                  year.toString(),

                  // style: TextStyle(
                  //   fontSize: widget.fontSize,
                  //   fontWeight:
                  //       isSelected ? FontWeight.bold : widget.fontWeight,
                  //   color:
                  //       isSelected
                  //           ? Theme.of(context).primaryColor
                  //           : widget.textColor,
                  // ),
                  style: FontManager.getCustomStyle(
                    fontFamily: FontManager.fontFamilyInter,
                    //fontWeight: FontManager.medium,
                    fontWeight:
                        isSelected ? FontManager.bold : widget.fontWeight,
                    fontSize: _getResponsiveFontSize(context),
                    color:
                        isSelected
                            ? Theme.of(context).primaryColor
                            : widget.textColor,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildYearGrid() {
    final currentYear = DateTime.now().year;
    final minYear = widget.minYear ?? (currentYear - 15);
    final maxYear = widget.maxYear ?? (currentYear + 15);

    // Generate years around the selected year for better UX
    final startYear = (_selectedYear - 15).clamp(minYear, maxYear);
    final endYear = (_selectedYear + 15).clamp(minYear, maxYear);

    final years = <int>[];
    for (int year = startYear; year <= endYear; year++) {
      years.add(year);
    }

    return Container(
      width: 280,
      constraints: const BoxConstraints(maxHeight: 300),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header with navigation arrows
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              IconButton(
                icon: const Icon(Icons.keyboard_arrow_up),
                onPressed: () {
                  setState(() {
                    _selectedYear = (_selectedYear - 15).clamp(
                      minYear,
                      maxYear,
                    );
                  });
                },
              ),
              Text(
                _selectedYear.toString(),
                // style: TextStyle(
                //   fontSize: widget.fontSize + 2,
                //   fontWeight: FontWeight.bold,
                //   color: widget.textColor,
                // ),
                style: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.bold,
                  color: widget.textColor,
                  fontSize: _getResponsiveFontSize(context),
                ),
              ),
              IconButton(
                icon: const Icon(Icons.keyboard_arrow_down),
                onPressed: () {
                  setState(() {
                    _selectedYear = (_selectedYear + 15).clamp(
                      minYear,
                      maxYear,
                    );
                  });
                },
              ),
            ],
          ),
          const SizedBox(height: 8),
          // Year grid
          Flexible(
            child: GridView.builder(
              shrinkWrap: true,
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                childAspectRatio: 2.5,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
              ),
              itemCount: years.length,
              itemBuilder: (context, index) {
                final year = years[index];
                final isSelected = year == _selectedYear;
                final isCurrentYear = year == currentYear;

                return GestureDetector(
                  onTap:
                      widget.isDisabled || widget.isReadOnly
                          ? null
                          : () {
                            setState(() {
                              _selectedYear = year;
                              _isOpen = false;
                            });
                            _notifyChange();
                            _removeYearPickerOverlay();
                          },
                  child: Container(
                    decoration: BoxDecoration(
                      color:
                          isSelected
                              ? Color(0xFF0058FF)
                              : isCurrentYear
                              ? Colors.grey.shade200
                              : Colors.transparent,
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(
                        color:
                            isSelected
                                ? Color(0xFF0058FF)
                                : Colors.grey.shade300,
                        width: 1,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        year.toString(),
                        // style: TextStyle(
                        //   fontSize: widget.fontSize - 2,
                        //   fontWeight:
                        //       isSelected ? FontWeight.bold : FontWeight.normal,
                        //   color:
                        //       isSelected
                        //           ? Colors.white
                        //           : isCurrentYear
                        //           ? Colors.black
                        //           : widget.textColor,
                        // ),
                        style: FontManager.getCustomStyle(
                          fontFamily: FontManager.fontFamilyInter,
                          fontWeight:
                              isSelected
                                  ? FontManager.bold
                                  : FontManager.medium,
                          color:
                              isSelected
                                  ? Colors.white
                                  : isCurrentYear
                                  ? Colors.black
                                  : widget.textColor,
                          fontSize: _getResponsiveFontSize(context),
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildYearSelector() {
    final minYear = widget.minYear ?? 1900;
    final maxYear = widget.maxYear ?? 2100;
    final years = List<int>.generate(maxYear - minYear + 1, (i) => minYear + i);

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (widget.showYearArrows)
          IconButton(
            icon: Icon(Icons.arrow_back_ios),
            onPressed:
                widget.isDisabled || widget.isReadOnly ? null : _decrementYear,
          ),
        if (widget.showYearDropdown)
          DropdownButton<int>(
            value: _selectedYear,
            onChanged:
                widget.isDisabled || widget.isReadOnly
                    ? null
                    : (int? newValue) {
                      if (newValue != null) {
                        setState(() {
                          _selectedYear = newValue;
                        });
                        _notifyChange();
                      }
                    },
            items:
                years.map<DropdownMenuItem<int>>((int year) {
                  return DropdownMenuItem<int>(
                    value: year,
                    child: Text(year.toString()),
                  );
                }).toList(),
          )
        else
          Text(
            _selectedYear.toString(),
            // style: TextStyle(
            //   fontSize: widget.fontSize,
            //   fontWeight: widget.fontWeight,
            // ),
            style: FontManager.getCustomStyle(
              fontFamily: FontManager.fontFamilyInter,
              fontWeight: FontManager.medium,
              fontSize: _getResponsiveFontSize(context),
            ),
          ),
        if (widget.showYearArrows)
          IconButton(
            icon: Icon(Icons.arrow_forward_ios),
            onPressed:
                widget.isDisabled || widget.isReadOnly ? null : _incrementYear,
          ),
      ],
    );
  }

  /// Handles hover state changes
  void _onHoverChange(bool isHovered) {
    setState(() {
      _isHovered = isHovered;
      if (widget.onHover != null) {
        widget.onHover!(isHovered);
      }
    });
  }

  /// Handles focus state changes
  void _onFocusChange(bool hasFocus) {
    setState(() {
      _isFocused = hasFocus;
      if (widget.onFocus != null) {
        widget.onFocus!(hasFocus);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // Apply dark theme if specified
    final effectiveTextColor =
        widget.isDarkTheme ? Colors.white : widget.textColor;
    final effectiveBackgroundColor =
        widget.isDarkTheme ? Colors.grey.shade800 : widget.backgroundColor;

    // Get responsive font size based on screen width
    double getResponsiveTitleFontSize(double screenWidth) {
      if (screenWidth > 1920) {
        return 18.0; // Extra Large
      } else if (screenWidth >= 1440) {
        return 16.0; // Large
      } else if (screenWidth >= 1280) {
        return 14.0; // Medium
      } else {
        return 14.0; // Default for very small screens
      }
    }

    final double screenWidth = MediaQuery.of(context).size.width;
    final double responsiveTitleFontSize = getResponsiveTitleFontSize(
      screenWidth,
    );

    // Get responsive icon size based on screen width
    double getResponsiveIconSize(double screenWidth) {
      if (screenWidth > 1920) {
        return 22.0; // Extra Large
      } else if (screenWidth >= 1440) {
        return 20.0; // Large
      } else if (screenWidth >= 1280) {
        return 18.0; // Medium
      } else if (screenWidth >= 768) {
        return 16.0; // Small
      } else {
        return 12.0; // Extra Small (fallback for very small screens)
      }
    }

    final double responsiveIconSize = getResponsiveIconSize(screenWidth);

    // Create the main content widget
    Widget content = CompositedTransformTarget(
      link: _layerLink,
      child: MouseRegion(
        onEnter: (_) => _onHoverChange(true),
        onExit: (_) => _onHoverChange(false),
        cursor: SystemMouseCursors.click,
        child: Focus(
          focusNode: _focusNode,
          autofocus: widget.autofocus,
          onFocusChange: _onFocusChange,
          child: GestureDetector(
            onTap: () {
              // Request focus when tapped
              _focusNode.requestFocus();

              // Handle year picker display
              if (widget.displayMode == YearDisplayMode.dropdown ||
                  widget.displayMode == YearDisplayMode.dialog ||
                  widget.displayMode == YearDisplayMode.bottomSheet ||
                  widget.displayMode == YearDisplayMode.slider ||
                  widget.displayMode == YearDisplayMode.numberPicker) {
                _showYearPicker();
              }

              // Call onTap callback if provided
              if (widget.onTap != null) {
                widget.onTap!();
              }

              // Call gestureTapCallback if provided
              if (widget.gestureTapCallback != null) {
                widget.gestureTapCallback!();
              }

              // Provide feedback if enabled
              if (widget.enableFeedback) {
                HapticFeedback.selectionClick();
              }
            },
            onDoubleTap: widget.onDoubleTap,
            onLongPress: widget.onLongPress,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              height: _getResponsiveHeight(context),
              padding: _getResponsivePadding(context),
              decoration: BoxDecoration(
                color: effectiveBackgroundColor,
                borderRadius: BorderRadius.circular(4),
                border:
                    widget.hasBorder
                        ? Border.all(
                          color:
                              _isHovered
                                  ? (widget.hoverColor ??
                                      const Color(0xFF0058FF))
                                  : _isFocused
                                  ? (widget.focusColor ??
                                      const Color(0xFF0058FF))
                                  : widget.borderColor,
                          width: widget.borderWidth,
                        )
                        : null,
                boxShadow:
                    widget.hasShadow
                        ? [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            blurRadius: widget.elevation,
                            offset: Offset(0, widget.elevation / 2),
                          ),
                        ]
                        : null,
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  if (widget.prefixText != null) ...[
                    Text(
                      widget.prefixText!,
                      // style: TextStyle(
                      //   color: effectiveTextColor.withOpacity(0.7),
                      //   fontSize: widget.fontSize,
                      // ),
                      style: FontManager.getCustomStyle(
                        fontFamily: FontManager.fontFamilyInter,
                        fontWeight: FontManager.medium,
                        color: effectiveTextColor.withOpacity(0.7),
                        fontSize: _getResponsiveFontSize(context),
                      ),
                    ),
                    const SizedBox(width: 8),
                  ],
                  Expanded(
                    child: Text(
                      _getFormattedYear(),
                      // style: TextStyle(
                      //   color: effectiveTextColor.withOpacity(0.6),
                      //   fontSize: responsiveTitleFontSize,
                      //   fontWeight: widget.fontWeight,
                      // ),
                      style: FontManager.getCustomStyle(
                        fontFamily: FontManager.fontFamilyInter,
                        fontWeight: FontManager.medium,
                        color: effectiveTextColor.withOpacity(0.6),
                        fontSize: _getResponsiveFontSize(context),
                      ),
                      textAlign: TextAlign.left,
                    ),
                  ),
                  if (widget.suffixText != null) ...[
                    const SizedBox(width: 8),
                    Text(
                      widget.suffixText!,
                      // style: TextStyle(
                      //   color: effectiveTextColor.withOpacity(0.7),
                      //   fontSize: widget.fontSize,
                      // ),
                      style: FontManager.getCustomStyle(
                        fontFamily: FontManager.fontFamilyInter,
                        fontWeight: FontManager.medium,
                        color: effectiveTextColor.withOpacity(0.7),
                        fontSize: _getResponsiveFontSize(context),
                      ),
                    ),
                  ],
                  if (widget.showCalendarIcon) ...[
                    const SizedBox(width: 8),
                    SvgPicture.asset(
                      _isHovered
                          ? 'assets/images/icon-date-hover.svg'
                          : 'assets/images/icon-date.svg',
                      package: 'ui_controls_library',
                      width: responsiveIconSize,
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
    );

    // Handle special display modes
    if (widget.displayMode == YearDisplayMode.chip) {
      content = Chip(
        label: Text(_getFormattedYear()),
        backgroundColor: effectiveBackgroundColor,
        // labelStyle: TextStyle(
        //   color: effectiveTextColor,
        //   fontSize: widget.fontSize,
        // ),
        labelStyle: FontManager.getCustomStyle(
          fontFamily: FontManager.fontFamilyInter,
          fontWeight: FontManager.medium,
          fontSize: _getResponsiveFontSize(context),
          color: effectiveTextColor,
        ),
        avatar:
            widget.showCalendarIcon
                ? Icon(Icons.calendar_today, size: widget.fontSize)
                : null,
        deleteIcon:
            widget.showClearButton
                ? Icon(Icons.clear, size: widget.fontSize)
                : null,
        onDeleted:
            widget.showClearButton && !widget.isDisabled && !widget.isReadOnly
                ? _clearSelection
                : null,
      );
    }

    // Build the final widget with label and helper text to match the image
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        content,
        if (widget.helperText != null) ...[
          const SizedBox(height: 4),
          Text(
            widget.helperText!,
            // style: TextStyle(
            //   color: effectiveTextColor.withOpacity(0.5),
            //   fontSize: widget.fontSize * 0.7,
            // ),
            style: FontManager.getCustomStyle(
              fontFamily: FontManager.fontFamilyInter,
              fontWeight: FontManager.medium,
              color: effectiveTextColor.withOpacity(0.5),
              fontSize: _getResponsiveFontSize(context),
            ),
          ),
        ],
        if (widget.errorText != null) ...[
          const SizedBox(height: 4),
          Text(
            widget.errorText!,
            // style: TextStyle(
            //   color: Colors.red,
            //   fontSize: widget.fontSize * 0.7,
            // ),
            // style: FontManager.getCustomStyle(
            //   fontFamily: FontManager.fontFamilyInter,
            //   fontWeight: FontManager.medium,
            //   color: Colors.red,
            //   fontSize: _getResponsiveFontSize(context),
            // ),
          ),
        ],
      ],
    );
  }
}

EdgeInsets _getResponsivePadding(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth >= 1440) {
    return const EdgeInsets.symmetric(
      horizontal: 16.0,
      vertical: 4.0,
    ); // Extra Large
  } else if (screenWidth >= 1280) {
    return const EdgeInsets.symmetric(horizontal: 12.0, vertical: 3.0); // Large
  } else if (screenWidth >= 768) {
    return const EdgeInsets.symmetric(horizontal: 8.0, vertical: 2.0); // Medium
  } else {
    return const EdgeInsets.symmetric(
      horizontal: 6.0,
      vertical: 1.0,
    ); // Default for very small screens
  }
}

double _getResponsiveHeight(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 56.0; // Extra Large (>1920px)
  } else if (screenWidth >= 1440) {
    return 48.0; // Large (1440-1920px)
  } else if (screenWidth >= 1280) {
    return 40.0; // Medium (1280-1366px)
  } else if (screenWidth >= 768) {
    return 32.0; // Small (768-1024px)
  } else {
    return 32.0; // Default for very small screens
  }
}

double _getResponsiveFontSize(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 18.0; // Extra Large
  } else if (screenWidth >= 1440) {
    return 16.0; // Large
  } else if (screenWidth >= 1280) {
    return 14.0; // Medium
  } else {
    return 12.0; // Default for very small screens
  }
}
