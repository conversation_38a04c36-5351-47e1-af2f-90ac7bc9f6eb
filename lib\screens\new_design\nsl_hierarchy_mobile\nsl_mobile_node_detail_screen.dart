import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/models/nsl_heirarchy_model1.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/models/nsl_tree_side_details_new.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/models/metric_info_model.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/nsl_hierarchy_provider.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/services/dummy_consolidated_service.dart';
import 'package:nsl/screens/new_design/nsl_hierarchy_mobile/speech_bubble_container.dart';
import 'package:nsl/theme/app_colors.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';

class NSLMobileNodeDetailScreen extends StatefulWidget {
  final NSLHierarchyData1 nodeData;
  final VoidCallback onBack;

  const NSLMobileNodeDetailScreen({
    super.key,
    required this.nodeData,
    required this.onBack,
  });

  @override
  State<NSLMobileNodeDetailScreen> createState() => _NSLMobileNodeDetailScreenState();
}

class _NSLMobileNodeDetailScreenState extends State<NSLMobileNodeDetailScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isTransactionsExpanded = false;
  DateTime? _selectedFromDate;
  DateTime? _selectedToDate;
  
  // Loading states
  bool _isLoadingNodeDetails = false;
  bool _isLoadingTransactions = false;
  
  // Data
  NslTreeSidePanel? _nodeDetails;
  MetricsInfo? _nodeTransactions;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _selectedFromDate = DateTime.now().subtract(const Duration(days: 30));
    _selectedToDate = DateTime.now();
    _loadNodeData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadNodeData() async {
    setState(() {
      _isLoadingNodeDetails = true;
      _isLoadingTransactions = true;
    });

    try {
      final provider = Provider.of<NslHierarchyProvider>(context, listen: false);
      
      // Load data using the provider's date range change method
      await provider.onDateRangeChanged(_selectedFromDate!, _selectedToDate!);
      
      // Get the loaded data from provider
      _nodeDetails = provider.nodeDetails;
      _nodeTransactions = provider.nodeTransactions;
      
    } catch (e) {
      print('Error loading node data: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingNodeDetails = false;
          _isLoadingTransactions = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            _buildDateSelection(),
            _buildLatestTransactionsContainer(),
            _buildTabNavigation(),
            Expanded(
              child: _buildTabContent(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // Node circle and speech bubble
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Node circle
                Container(
                  width: 34,
                  height: 34,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: widget.nodeData.level == 'M4' 
                          ? const Color(0xFF0058FF) 
                          : NSLNode.getLevelColorMobile(widget.nodeData.level),
                      width: 3,
                    ),
                  ),
                  child: Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: widget.nodeData.level == 'M4' 
                          ? const Color(0xFF0058FF) 
                          : NSLNode.getLevelColorMobile(widget.nodeData.level),
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Colors.white,
                        width: 2,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        widget.nodeData.level,
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodyMedium(context),
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: AppColors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(width: 12),
                
                // Speech bubble with node info
                Expanded(
                  child: SpeechBubbleContainer(
                    tailOffset: -20.0,
                    backgroundColor: Colors.white,
                    borderColor: const Color(0xFF0058FF),
                    borderWidth: 2.0,
                    showTail: true,
                    tailDirection: TailDirection.left,
                    tailSize: 12.0,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Title section
                        Container(
                          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
                          child: Text(
                            widget.nodeData.title.length > 20 
                                ? '${widget.nodeData.title.substring(0, 15)}...' 
                                : widget.nodeData.title,
                            textAlign: TextAlign.center,
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.titleLarge(context),
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: AppColors.black,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        
                        // ID and NP row
                        Container(
                          margin: const EdgeInsets.only(left: 1, right: 1, bottom: 1),
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                          decoration: BoxDecoration(
                            color: NSLNode.getLevelColor(widget.nodeData.level),
                            border: Border.all(
                              color: Colors.grey.shade300,
                            ),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'ID: ${widget.nodeData.employeeId ?? widget.nodeData.id}',
                                style: FontManager.getCustomStyle(
                                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: AppColors.black,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Text(
                                'NP: ${widget.nodeData.totalBets}',
                                style: FontManager.getCustomStyle(
                                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: AppColors.black,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Back button
          GestureDetector(
            onTap: widget.onBack,
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.arrow_back,
                size: 20,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateSelection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => _selectFromDate(context),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(color: const Color(0xFFB4B4B4)),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.calendar_today, size: 14, color: Colors.black),
                  const SizedBox(width: 6),
                  Text(
                    _formatDate(_selectedFromDate),
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodySmall(context),
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(width: 8),
          
          Text(
            'To',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodySmall(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
              fontWeight: FontWeight.w400,
            ),
          ),
          
          const SizedBox(width: 8),
          
          GestureDetector(
            onTap: () => _selectToDate(context),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(color: const Color(0xFFB4B4B4)),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.calendar_today, size: 14, color: Colors.black),
                  const SizedBox(width: 6),
                  Text(
                    _formatDate(_selectedToDate),
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodySmall(context),
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLatestTransactionsContainer() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Header row
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Latest Transactions',
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.titleMedium(context),
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _isTransactionsExpanded = !_isTransactionsExpanded;
                    });
                  },
                  child: Text(
                    _isTransactionsExpanded ? 'View Back' : 'See all',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodyMedium(context),
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: const Color(0xFF0058FF),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Metrics cards
          if (!_isTransactionsExpanded) _buildCompactMetrics() else _buildExpandedMetrics(),
          
          // GO/LO row
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: _buildGoLoCard(
                    'Total GO\'s',
                    _isLoadingNodeDetails 
                        ? null 
                        : _nodeDetails?.result?.goCount?.toString() ?? '45',
                    _isLoadingNodeDetails,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildGoLoCard(
                    'Total LO\'s',
                    _isLoadingNodeDetails 
                        ? null 
                        : _nodeDetails?.result?.loCount?.toString() ?? '134',
                    _isLoadingNodeDetails,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompactMetrics() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: _buildMetricCard(
              'Total Transactions',
              _isLoadingTransactions 
                  ? null 
                  : _formatTransactionCount(_nodeTransactions?.result?.totalTransactions ?? 2345500),
              _isLoadingTransactions,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildMetricCard(
              'M3 Nodes',
              '12',
              false,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExpandedMetrics() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildMetricCard(
                  'Total Transactions',
                  _isLoadingTransactions 
                      ? null 
                      : _formatTransactionCount(_nodeTransactions?.result?.totalTransactions ?? 2345500),
                  _isLoadingTransactions,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildMetricCard(
                  'M3 Nodes',
                  '12',
                  false,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _buildMetricCard(
                  'Revenue',
                  _isLoadingTransactions 
                      ? null 
                      : '\$${_formatNumber(_nodeTransactions?.result?.revenue?.toDouble() ?? 7000000)}',
                  _isLoadingTransactions,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildMetricCard(
                  'Cost',
                  _isLoadingTransactions 
                      ? null 
                      : '\$${_formatNumber(_nodeTransactions?.result?.cost ?? 4320000)}',
                  _isLoadingTransactions,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Container(
            width: double.infinity,
            child: _buildMetricCard(
              'Margin',
              _isLoadingTransactions 
                  ? null 
                  : '${(_nodeTransactions?.result?.margin ?? 39.2).toStringAsFixed(1)}%',
              _isLoadingTransactions,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetricCard(String title, String? value, bool isLoading) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodySmall(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
              fontWeight: FontWeight.w400,
            ),
          ),
          const SizedBox(height: 4),
          if (isLoading)
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          else
            Text(
              value ?? 'N/A',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.titleMedium(context),
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
                fontWeight: FontWeight.w600,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildGoLoCard(String title, String? value, bool isLoading) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodySmall(context),
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                  fontWeight: FontWeight.w400,
                ),
              ),
              const SizedBox(height: 4),
              if (isLoading)
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              else
                Text(
                  value ?? 'N/A',
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.titleLarge(context),
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                    fontWeight: FontWeight.w600,
                  ),
                ),
            ],
          ),
          const Icon(
            Icons.arrow_forward,
            color: Color(0xFF0058FF),
            size: 20,
          ),
        ],
      ),
    );
  }

  Widget _buildTabNavigation() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: const Color(0xFFE4EDFF),
        borderRadius: BorderRadius.circular(8),
      ),
      child: TabBar(
        controller: _tabController,
        dividerColor: Colors.transparent,
        indicator: BoxDecoration(
          color: const Color(0xFF0058FF),
          borderRadius: BorderRadius.circular(6),
        ),
        labelStyle: FontManager.getCustomStyle(
          fontSize: ResponsiveFontSizes.bodySmall(context),
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Colors.white,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: FontManager.getCustomStyle(
          fontSize: ResponsiveFontSizes.bodySmall(context),
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Colors.black,
          fontWeight: FontWeight.w400,
        ),
        labelColor: Colors.white,
        unselectedLabelColor: Colors.black,
        tabs: const [
          Tab(text: 'Standalone'),
          Tab(text: 'Consolidated'),
          Tab(text: 'BET Breakdown'),
        ],
      ),
    );
  }

  Widget _buildTabContent() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildStandaloneTab(),
        _buildConsolidatedTab(),
        _buildBetBreakdownTab(),
      ],
    );
  }

  Widget _buildStandaloneTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildFinancialCards(),
          const SizedBox(height: 16),
          _buildIncomeStatement(),
          const SizedBox(height: 16),
          _buildBalanceSheet(),
        ],
      ),
    );
  }

  Widget _buildFinancialCards() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildFinancialCard(
                'Total Revenue',
                _isLoadingTransactions 
                    ? null 
                    : '\$${_formatNumber(_nodeTransactions?.result?.revenue?.toDouble() ?? 7000000)}',
                '+12.5% vs last month',
                Colors.green,
                _isLoadingTransactions,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildFinancialCard(
                'Net Margin',
                _isLoadingTransactions 
                    ? null 
                    : '${(_nodeTransactions?.result?.margin ?? 39.2).toStringAsFixed(1)}%',
                '2.1% vs last month',
                Colors.blue,
                _isLoadingTransactions,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildFinancialCard(
                'Total Transactions',
                _isLoadingTransactions 
                    ? null 
                    : _formatTransactionCount(_nodeTransactions?.result?.totalTransactions ?? 15234),
                '+8.7% vs last month',
                Colors.orange,
                _isLoadingTransactions,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildFinancialCard(
                'Utilization',
                '88.5%',
                '+3.2% vs last month',
                Colors.purple,
                false,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFinancialCard(String title, String? value, String trend, Color color, bool isLoading) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  title,
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodySmall(context),
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
              if (isLoading)
                SizedBox(
                  width: 12,
                  height: 12,
                  child: CircularProgressIndicator(
                    strokeWidth: 1.5,
                    valueColor: AlwaysStoppedAnimation<Color>(color),
                  ),
                )
              else
                Text(
                  value ?? 'N/A',
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodyMedium(context),
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                    fontWeight: FontWeight.w600,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            trend,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodySmall(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIncomeStatement() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Income Statement - Standalone',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'For the Month Ended December 30, 2024',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodySmall(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w400,
            ),
          ),
          const Divider(color: Color(0xFFB4B4B4)),
          const SizedBox(height: 8),
          _buildIncomeStatementContent(),
        ],
      ),
    );
  }

  Widget _buildIncomeStatementContent() {
    if (_isLoadingTransactions) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(20),
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Column(
      children: [
        _buildIncomeStatementRow('Service Revenue - Application Development', '\$3,150,000', '45.0% of Revenue'),
        _buildIncomeStatementRow('Service Revenue - QA & Testing', '\$3,150,000', '45.0% of Revenue'),
        _buildIncomeStatementRow('Service Revenue - Infrastructure Services', '\$3,150,000', '45.0% of Revenue'),
      ],
    );
  }

  Widget _buildIncomeStatementRow(String item, String amount, String percentage) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              item,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodySmall(context),
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              amount,
              textAlign: TextAlign.right,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodySmall(context),
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              percentage,
              textAlign: TextAlign.right,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodySmall(context),
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBalanceSheet() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Balance Sheet - Standalone',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          const Divider(color: Color(0xFFB4B4B4)),
          const SizedBox(height: 8),
          if (_isLoadingTransactions)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(20),
                child: CircularProgressIndicator(),
              ),
            )
          else
            const Text('Balance sheet data will be displayed here'),
        ],
      ),
    );
  }

  Widget _buildConsolidatedTab() {
    return FutureBuilder<List<dynamic>>(
      future: DummyConsolidatedService.loadDummyData(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Text(
              'Error loading consolidated data',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodyMedium(context),
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.red,
                fontWeight: FontWeight.w400,
              ),
            ),
          );
        }

        final dataList = snapshot.data ?? [];
        Map<String, dynamic>? nodeData;
        
        for (var item in dataList) {
          if (item is Map<String, dynamic> && item['id'] == widget.nodeData.id) {
            nodeData = item;
            break;
          }
        }

        String totalGo = nodeData?['consolidated']?['total_gos'] ?? '45';
        String internalElimination = nodeData?['consolidated']?['internal_elimination'] ?? '134';
        String efficiency = nodeData?['consolidated']?['effeciency'] ?? '88.5%';
        String teamCoordination = nodeData?['consolidated']?['team_coordination'] ?? '39.2%';

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: _buildConsolidatedMetricCard('Total Go', totalGo),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildConsolidatedMetricCard('Internal Elimination', internalElimination),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: _buildConsolidatedMetricCard('Efficiency', efficiency),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildConsolidatedMetricCard('Team Coordination', teamCoordination),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildConsolidatedMetricCard(String title, String value) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodySmall(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
              fontWeight: FontWeight.w400,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBetBreakdownTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'BET Breakdown',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          _buildBetBreakdownCard('GO\'s', widget.nodeData.betBreakdown.gos.toString()),
          const SizedBox(height: 12),
          _buildBetBreakdownCard('LO\'s', widget.nodeData.betBreakdown.los.toString()),
          const SizedBox(height: 12),
          _buildBetBreakdownCard('NP Functions', widget.nodeData.betBreakdown.npFunctions.toString()),
          const SizedBox(height: 12),
          _buildBetBreakdownCard('Input/Output Stacks', widget.nodeData.betBreakdown.inputOutputStacks.toString()),
          const SizedBox(height: 12),
          _buildBetBreakdownCard('Subordinate NSL', widget.nodeData.betBreakdown.subordinateNsl.toString()),
        ],
      ),
    );
  }

  Widget _buildBetBreakdownCard(String title, String value) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
              fontWeight: FontWeight.w400,
            ),
          ),
          Text(
            value,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  String _formatDate(DateTime? date) {
    if (date == null) return 'DD/MM/YY';
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year.toString().substring(2)}';
  }

  String _formatNumber(double value) {
    if (value >= 1000000) {
      return '${(value / 1000000).toStringAsFixed(1)}M';
    } else if (value >= 1000) {
      return '${(value / 1000).toStringAsFixed(1)}K';
    } else {
      return value.toStringAsFixed(0);
    }
  }

  String _formatTransactionCount(int value) {
    if (value >= 1000000) {
      return '${(value / 1000000).toStringAsFixed(1)}M';
    } else if (value >= 1000) {
      return '${(value / 1000).toStringAsFixed(1)}K';
    } else {
      return value.toString();
    }
  }

  Future<void> _selectFromDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedFromDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );
    if (picked != null && picked != _selectedFromDate) {
      setState(() {
        _selectedFromDate = picked;
      });
      _loadNodeData(); // Reload data with new date range
    }
  }

  Future<void> _selectToDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedToDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );
    if (picked != null && picked != _selectedToDate) {
      setState(() {
        _selectedToDate = picked;
      });
      _loadNodeData(); // Reload data with new date range
    }
  }
}
