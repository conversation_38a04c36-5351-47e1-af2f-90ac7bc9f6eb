import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/models/nsl_heirarchy_model1.dart';
import 'package:nsl/screens/new_design/nsl_hierarchy_mobile/nsl_mobile_node_card.dart';
import 'package:nsl/screens/new_design/nsl_hierarchy_mobile/nsl_mobile_node_detail_screen.dart';

class NSLHierarchyMobileChart extends StatefulWidget {
  final NSLNode rootNode;
  final Set<String> expandedNodes;
  final Function(String) onNodeInfoTap;
  final Function(NSLHierarchyData1) onNodeCircleTap;
  final String? selectedNodeId;
  const NSLHierarchyMobileChart({
    super.key,
    required this.rootNode,
    required this.expandedNodes,
    required this.onNodeInfoTap,
    required this.onNodeCircleTap,
    this.selectedNodeId,
  });
  @override
  State<NSLHierarchyMobileChart> createState() =>
      _NSLHierarchyMobileChartState();
}

class _NSLHierarchyMobileChartState extends State<NSLHierarchyMobileChart>
    with SingleTickerProviderStateMixin {
  final Map<String, double> _measuredChildrenHeights = {};
  Set<String> _previousExpandedNodes = {};
  String? _localSelectedNodeId; // Add local state for selected node
  @override
  void initState() {
    super.initState();
    _previousExpandedNodes = Set.from(widget.expandedNodes);
    _localSelectedNodeId = widget.selectedNodeId;
  }

  @override
  void didUpdateWidget(NSLHierarchyMobileChart oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Update local selected node ID if it changed from parent
    if (widget.selectedNodeId != oldWidget.selectedNodeId) {
      _localSelectedNodeId = widget.selectedNodeId;
    }
    // Check if expanded nodes have changed
    if (!_setsEqual(_previousExpandedNodes, widget.expandedNodes)) {
      // Only clear highlighted path if a node in the current path was collapsed
      _clearHighlightedPathIfInvalid();

      // Clear height measurements for nodes that are no longer expanded
      _cleanupCollapsedNodeHeights();
      _previousExpandedNodes = Set.from(widget.expandedNodes);
    }
  }

  bool _setsEqual(Set<String> set1, Set<String> set2) {
    if (set1.length != set2.length) return false;
    return set1.every((element) => set2.contains(element));
  }

  void _clearHighlightedPathIfInvalid() {
    final selectedId = _localSelectedNodeId ?? widget.selectedNodeId;
    if (selectedId == null) return;
    // Get the current path to the selected node
    final path = _getPathToNode(widget.rootNode, selectedId);

    // If the path is empty, it means the selected node is no longer reachable
    if (path.isEmpty) {
      setState(() {
        _localSelectedNodeId = null;
      });
      return;
    }
    // Check if any node in the path (except the leaf/selected node) is collapsed
    // This includes checking if the selected node itself is collapsed when it has children
    for (int i = 0; i < path.length; i++) {
      final nodeId = path[i];
      final node = _findNodeById(widget.rootNode, nodeId);

      if (node != null && node.children.isNotEmpty) {
        // If this node has children but is not expanded, the path is broken
        if (!widget.expandedNodes.contains(nodeId)) {
          setState(() {
            _localSelectedNodeId = null;
          });
          return;
        }
      }
    }
  }

  void _cleanupCollapsedNodeHeights() {
    // Remove height measurements for nodes that are no longer expanded
    final keysToRemove = <String>[];
    for (final nodeId in _measuredChildrenHeights.keys) {
      if (!widget.expandedNodes.contains(nodeId)) {
        keysToRemove.add(nodeId);
      }
    }
    for (final key in keysToRemove) {
      _measuredChildrenHeights.remove(key);
    }
    // Force immediate rebuild if any heights were removed
    if (keysToRemove.isNotEmpty) {
      setState(() {});
    }
  }

  // Get path from root to selected node
  List<String> _getPathToNode(NSLNode node, String targetId,
      [List<String>? currentPath]) {
    currentPath ??= [];
    currentPath.add(node.id);
    if (node.id == targetId) {
      return List.from(currentPath);
    }
    for (var child in node.children) {
      final path = _getPathToNode(child, targetId, List.from(currentPath));
      if (path.isNotEmpty) {
        return path;
      }
    }
    return [];
  }

  // Check if node is in the highlighted path
  bool _isNodeInHighlightedPath(String nodeId) {
    final selectedId = _localSelectedNodeId ?? widget.selectedNodeId;
    if (selectedId == null) return false;
    final path = _getPathToNode(widget.rootNode, selectedId);
    return path.contains(nodeId);
  }

  // Check if connection should be highlighted
  bool _shouldHighlightConnection(String parentId, String childId) {
    final selectedId = _localSelectedNodeId ?? widget.selectedNodeId;
    if (selectedId == null) return false;
    final path = _getPathToNode(widget.rootNode, selectedId);
    final parentIndex = path.indexOf(parentId);
    final childIndex = path.indexOf(childId);
    return parentIndex != -1 &&
        childIndex != -1 &&
        childIndex == parentIndex + 1;
  }

  // NEW METHOD: Check if this node's downward connecting line should be highlighted
  bool _shouldHighlightNodeConnectingLine(String nodeId) {
    final selectedId = _localSelectedNodeId ?? widget.selectedNodeId;
    if (selectedId == null) return false;
    
    final path = _getPathToNode(widget.rootNode, selectedId);
    final nodeIndex = path.indexOf(nodeId);
    
    // Only highlight if this node is in the path AND it has a child in the path
    if (nodeIndex == -1 || nodeIndex >= path.length - 1) return false;
    
    final node = _findNodeById(widget.rootNode, nodeId);
    if (node == null) return false;
    
    // Check if the next node in the path is a direct child of this node
    final nextNodeId = path[nodeIndex + 1];
    return node.children.any((child) => child.id == nextNodeId);
  }

  void _handleNodeInfoTap(String nodeId) {
    // Update local state immediately for instant UI feedback
    setState(() {
      _localSelectedNodeId = nodeId;
    });

    // Call the parent callback
    widget.onNodeInfoTap(nodeId);
  }

  void _navigateToDetailScreen(NSLHierarchyData1 nodeData) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => NSLMobileNodeDetailScreen(
          nodeData: nodeData,
          onBack: () {
            Navigator.of(context).pop();
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: ScrollConfiguration(
        behavior: ScrollConfiguration.of(context).copyWith(
          scrollbars: false, // This hides the scrollbar
        ),
        child: SingleChildScrollView(
          scrollDirection: Axis.vertical,
          padding: const EdgeInsets.all(14.0),
          child: IntrinsicWidth(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildNodeTree(widget.rootNode, 0, null),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNodeTree(NSLNode node, int depth, String? parentId) {
    final isExpanded = widget.expandedNodes.contains(node.id);
    final hasChildren = node.children.isNotEmpty;
    final isInHighlightedPath = _isNodeInHighlightedPath(node.id);
    final shouldHighlightConnectionFromParent = parentId != null
        ? _shouldHighlightConnection(parentId, node.id)
        : false;
    final selectedId = _localSelectedNodeId ?? widget.selectedNodeId;
    final isSelected = selectedId == node.id;
    final shouldHighlightConnectingLine = _shouldHighlightNodeConnectingLine(node.id); // ADD THIS
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Current node with potential connection from parent
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Connection from parent (horizontal line)
            if (depth > 0)
              SizedBox(
                width: 40,
                height: 80,
                child: CustomPaint(
                  painter: ParentConnectionPainter(
                    shouldHighlight: shouldHighlightConnectionFromParent,
                  ),
                ),
              ),
            // Node card - Use IntrinsicWidth to allow natural sizing
            IntrinsicWidth(
              child: NSLMobileNodeCard(
                node: node,
                isExpanded: isExpanded,
                hasChildren: hasChildren,
                onInfoTap: () => _handleNodeInfoTap(node.id),
                onCircleTap: () => _navigateToDetailScreen(node.originalData),
                showConnectingLine: false,
                isHighlighted: isInHighlightedPath,
                isSelected: isSelected,
                shouldHighlightConnectingLine: shouldHighlightConnectingLine, // PASS THE NEW PARAMETER
              ),
            ),
          ],
        ),
        // Children connections and nodes (if expanded)
        if (isExpanded && hasChildren) ...[
          // Remove the gap to connect parent's vertical line to children's vertical line
          _buildChildrenWithConnections(node, depth),
        ],
      ],
    );
  }

  Widget _buildChildrenWithConnections(NSLNode parentNode, int parentDepth) {
    final children = parentNode.children;
    if (children.isEmpty) return const SizedBox.shrink();
    // Check if this node is actually expanded, if not, don't use cached height
    final isParentExpanded = widget.expandedNodes.contains(parentNode.id);
    // Check if the last child is expanded - if so, don't extend the parent's vertical line
    final lastChild = children.last;
    final isLastChildExpanded = widget.expandedNodes.contains(lastChild.id);
    final measuredHeight = isParentExpanded
        ? (_measuredChildrenHeights[parentNode.id] ?? 0.0)
        : 0.0;
    // Find which child (if any) is in the highlighted path
    final selectedId = _localSelectedNodeId ?? widget.selectedNodeId;
    int? highlightedChildIndex;
    if (selectedId != null) {
      final path = _getPathToNode(widget.rootNode, selectedId);
      for (int i = 0; i < children.length; i++) {
        if (path.contains(children[i].id)) {
          highlightedChildIndex = i;
          break;
        }
      }
    }
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (parentDepth > 0) const SizedBox(width: 40),
        const SizedBox(
            width: 17), // Position to align with parent circle center
        // Vertical line area (height comes from actual children height)
        SizedBox(
          width: 2,
          height: measuredHeight,
          child: CustomPaint(
            painter: ChildrenConnectionPainter(
              childrenCount: children.length,
              shouldHighlight: _isNodeInHighlightedPath(parentNode.id),
              highlightedChildren: children
                  .map(
                    (child) =>
                        _shouldHighlightConnection(parentNode.id, child.id),
                  )
                  .toList(),
              totalHeight: measuredHeight,
              isLastChildExpanded: isLastChildExpanded,
              highlightedChildIndex:
                  highlightedChildIndex, // Pass the highlighted child index
            ),
          ),
        ),
        // Children column — this is what we want to measure
        SizeReportingWidget(
          onSizeChanged: (height) {
            // Only update height if the parent is still expanded
            if (isParentExpanded &&
                _measuredChildrenHeights[parentNode.id] != height) {
              setState(() {
                _measuredChildrenHeights[parentNode.id] = height;
              });
            } else if (!isParentExpanded) {
              // If parent is not expanded, clear the height immediately
              setState(() {
                _measuredChildrenHeights.remove(parentNode.id);
              });
            }
          },
          child: IntrinsicWidth(
            child: Column(
              children: children.asMap().entries.map((entry) {
                final child = entry.value;
                return Padding(
                  padding: EdgeInsets.only(
                    bottom: entry.key < children.length - 1 ? 8 : 0,
                  ),
                  child: _buildNodeTree(child, parentDepth + 1, parentNode.id),
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  bool _isLastChild(NSLNode node, String? parentId) {
    if (parentId == null) return true;
    NSLNode? parent = _findNodeById(widget.rootNode, parentId);
    if (parent == null) return true;
    return parent.children.isNotEmpty && parent.children.last.id == node.id;
  }

  NSLNode? _findNodeById(NSLNode node, String id) {
    if (node.id == id) return node;
    for (var child in node.children) {
      final found = _findNodeById(child, id);
      if (found != null) return found;
    }
    return null;
  }
}

// Painter for horizontal connection line from parent's vertical line to child
class ParentConnectionPainter extends CustomPainter {
  final bool shouldHighlight;
  final double leftShift;
  ParentConnectionPainter({
    required this.shouldHighlight,
    this.leftShift = 2.0,
  });
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke
      ..color =
          shouldHighlight ? const Color(0xFF0058FF) : const Color(0xFFB4B4B4);
    final fillPaint = Paint()
      ..style = PaintingStyle.fill
      ..color =
          shouldHighlight ? const Color(0xFF0058FF) : const Color(0xFFB4B4B4);
    final path = Path();
    // Shifted starting point
    path.moveTo(-leftShift, 0);
    // Curve from vertical to horizontal
    path.quadraticBezierTo(
      -leftShift,
      size.height * 0.2,
      size.width - 10 - leftShift,
      size.height * 0.2,
    );
    canvas.drawPath(path, paint);
    // Right-pointing triangle (arrowhead)
    const arrowWidth = 6.0;
    const arrowHeight = 6.0;
    final arrowTip = Offset(size.width - leftShift, size.height * 0.2);
    final arrowPath = Path();
    arrowPath.moveTo(arrowTip.dx, arrowTip.dy);
    arrowPath.lineTo(arrowTip.dx - arrowWidth, arrowTip.dy - arrowHeight / 2);
    arrowPath.lineTo(arrowTip.dx - arrowWidth, arrowTip.dy + arrowHeight / 2);
    arrowPath.close();
    canvas.drawPath(arrowPath, fillPaint);
  }

  @override
  bool shouldRepaint(ParentConnectionPainter oldDelegate) {
    return oldDelegate.shouldHighlight != shouldHighlight ||
        oldDelegate.leftShift != leftShift;
  }
}

// Painter for vertical line from parent and horizontal connections to children
class ChildrenConnectionPainter extends CustomPainter {
  final int childrenCount;
  final bool shouldHighlight;
  final List<bool> highlightedChildren;
  final double totalHeight;
  final bool isLastChildExpanded;
  final int?
      highlightedChildIndex; // Index of the child that should be highlighted
  ChildrenConnectionPainter({
    required this.childrenCount,
    required this.shouldHighlight,
    required this.highlightedChildren,
    required this.totalHeight,
    required this.isLastChildExpanded,
    this.highlightedChildIndex,
  });
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;
    const double verticalLineX = 0;
    const double nodeHeight = 80.0;
    const double spacing = 8.0;
    const double childCircleCenterY = 80.0;
    final double lastDirectChildCenterY = totalHeight > 0
        ? (totalHeight - childCircleCenterY).toDouble()
        : (((childrenCount - 1) * (nodeHeight + spacing)) + childCircleCenterY)
            .toDouble();
    final double lastChildCenterY =
        ((childrenCount - 1) * (nodeHeight + spacing)) + childCircleCenterY;
    final double lineEndY =
        isLastChildExpanded ? lastChildCenterY : lastDirectChildCenterY;
    if (highlightedChildIndex != null && shouldHighlight) {
      // Calculate the Y position where the highlighted child's arrow branches off
      final double highlightedChildCenterY =
          (highlightedChildIndex! * (nodeHeight + spacing))  
         +
              childCircleCenterY;

      // Only highlight up to the branch point, not beyond
      final double highlightEndY = highlightedChildCenterY ;

      // Draw highlighted line from start to the highlighted child's branch point only
      paint.color = const Color(0xFF0058FF);
      canvas.drawLine(
        Offset(verticalLineX, 0),
        Offset(verticalLineX, highlightEndY),
        paint,
      );

      // Draw remaining line in normal color (if there are more children below)
      if (highlightEndY < lineEndY) {
        paint.color = const Color(0xFFB4B4B4);
        canvas.drawLine(
          Offset(verticalLineX, highlightEndY),
          Offset(verticalLineX, lineEndY),
          paint,
        );
      }
    } else {
      // Draw entire line in normal color
      paint.color = const Color(0xFFB4B4B4);
      canvas.drawLine(
        Offset(verticalLineX, 0),
        Offset(verticalLineX, lineEndY),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(ChildrenConnectionPainter oldDelegate) {
    return oldDelegate.shouldHighlight != shouldHighlight ||
        oldDelegate.childrenCount != childrenCount ||
        oldDelegate.highlightedChildren != highlightedChildren ||
        oldDelegate.totalHeight != totalHeight ||
        oldDelegate.isLastChildExpanded != isLastChildExpanded ||
        oldDelegate.highlightedChildIndex != highlightedChildIndex;
  }
}

class SizeReportingWidget extends StatefulWidget {
  final Widget child;
  final ValueChanged<double> onSizeChanged;
  const SizeReportingWidget({
    super.key,
    required this.child,
    required this.onSizeChanged,
  });
  @override
  State<SizeReportingWidget> createState() => _SizeReportingWidgetState();
}

class _SizeReportingWidgetState extends State<SizeReportingWidget> {
  final _key = GlobalKey();
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) => _reportSize());
  }

  @override
  void didUpdateWidget(covariant SizeReportingWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    WidgetsBinding.instance.addPostFrameCallback((_) => _reportSize());
  }

  void _reportSize() {
    final context = _key.currentContext;
    if (context != null) {
      final size = context.size;
      if (size != null) {
        widget.onSizeChanged(size.height);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      key: _key,
      child: widget.child,
    );
  }
}
