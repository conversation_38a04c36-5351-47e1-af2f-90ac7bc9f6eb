import 'package:flutter/material.dart';
import 'package:nsl/screens/new_design/nsl_hierarchy_mobile/speech_bubble_container.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/models/nsl_heirarchy_model1.dart';
import 'package:nsl/theme/app_colors.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';

class NSLMobileNodeCard extends StatelessWidget {
  final NSLNode node;
  final bool isExpanded;
  final bool hasChildren;
  final VoidCallback onInfoTap;
  final VoidCallback onCircleTap;
  final bool showConnectingLine;
  final bool isHighlighted;
  final bool isSelected;
  final bool shouldHighlightConnectingLine; // Add this new parameter

  const NSLMobileNodeCard({
    super.key,
    required this.node,
    required this.isExpanded,
    required this.hasChildren,
    required this.onInfoTap,
    required this.onCircleTap,
    this.showConnectingLine = false,
    this.isHighlighted = false,
    this.isSelected = false,
    this.shouldHighlightConnectingLine = false, // Add this parameter
  });

  @override
  Widget build(BuildContext context) {
    // Determine border color based on state
    Color borderColor;
    if (isSelected) {
      borderColor = const Color(0xFF0058FF); // Blue for selected
    } else if (isHighlighted) {
      borderColor = const Color(0xFF0058FF); // Blue for highlighted path
    } else if (isExpanded) {
      borderColor = const Color(0xFF0058FF); // Blue for expanded
    } else {
      borderColor = Colors.grey.shade300; // Default gray
    }

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Node circle with level indicator and connecting line
        Column(
          children: [
            GestureDetector(
              // onTap: onCircleTap,
              child: Container(
                width: 34,
                height: 34,
                decoration: BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isHighlighted || isSelected 
                        ? const Color(0xFF0058FF) 
                        : node.levelColorMobile,
                    width: isHighlighted || isSelected ? 3 : 1,
                  )
                ),
                
                child: Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: isHighlighted || isSelected 
                        ? const Color(0xFF0058FF) 
                        : node.levelColorMobile,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.white,
                       width: 2,
                    ),
                   
                  ),
                  child: Center(
                    child: Text(
                      node.level,
                      style:  FontManager.getCustomStyle(
                                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: AppColors.white,
                                  fontWeight: FontWeight.w600,
                                ),
                     
                    ),
                  ),
                ),
              ),
            ),
            // Vertical connecting line with downward arrow below the circle (for parent nodes with children)
            if (hasChildren && isExpanded)
              SizedBox(
                width: 10,
                height: 60,
                child: CustomPaint(
                  painter: DownwardArrowLinePainter(
                    color: shouldHighlightConnectingLine // Use the new parameter instead
                        ? const Color(0xFF0058FF) 
                        : const Color(0xFFB4B4B4),
                  ),
                ),
              ),
          ],
        ),
        
        const SizedBox(width: 12),
      
        // Node content - Single unified container
        IntrinsicWidth(
          child: SpeechBubbleContainer(
            tailOffset: -20.0,
            backgroundColor: Colors.white,
            borderColor: borderColor,
            borderWidth: isHighlighted || isSelected ? 2.0 : 1.0,
            showTail: true,
            tailDirection: TailDirection.left,
            tailSize: 12.0,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Title section
                GestureDetector(
                  onTap: onCircleTap,
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 10,horizontal: 16),
                    child: Text(
                       node.title.length > 20 ? '${node.title.substring(0, 15)}...' : node.title,
                      textAlign: TextAlign.center,
                       style:  FontManager.getCustomStyle(
                                        fontSize: ResponsiveFontSizes.titleLarge(context),
                                        fontFamily: FontManager.fontFamilyTiemposText,
                                        color: AppColors.black,
                                        fontWeight: FontWeight.w500,
                                      ),
                    ),
                  ),
                ),
                
                // ID and NP row
                GestureDetector(
                  onTap: onInfoTap,
                  child: Container(
                    margin: EdgeInsets.only(left:1,right:1,bottom:1,),
                    padding: const EdgeInsets.symmetric(horizontal:8,vertical:12),
                    decoration: BoxDecoration(
                      color: 
                      // isHighlighted || isSelected 
                      //     ? Colors.red :
                          node.levelColor,
                      border: Border.all(
                        color: 
                        // isHighlighted 
                        //     ? const Color(0xFF0058FF)
                            Colors.grey.shade300,
                      ),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'ID: ${node.originalData.employeeId}',
                          style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.bodyMedium(context),
                                      fontFamily: FontManager.fontFamilyTiemposText,
                                      color: AppColors.black,
                                      fontWeight: FontWeight.w400,
                                    ),
                        ),
                        const SizedBox(width: 16),
                        Text(
                          'NP: ${node.totalBets}',
                          style:  FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.bodyMedium(context),
                                      fontFamily: FontManager.fontFamilyTiemposText,
                                      color: AppColors.black,
                                      fontWeight: FontWeight.w400,
                                    ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

// Custom painter for downward arrow with line
class DownwardArrowLinePainter extends CustomPainter {
  final Color color;
  final double topPadding;

  DownwardArrowLinePainter({
    required this.color,
    this.topPadding = 4.0, // default padding from top
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final fillPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final centerX = size.width / 2;
    const arrowWidth = 6.0;
    const arrowHeight = 6.0;
    const gap = 1.0;

    // Apply top padding to shift everything down
    final arrowTopY = topPadding;
    final arrowBottomY = arrowTopY + arrowHeight;

    // Draw downward arrow at the top with padding
    final arrowPath = Path();
    final arrowTip = Offset(centerX, arrowBottomY); // arrow tip (↓)
    final left = Offset(centerX - arrowWidth / 2, arrowTopY);
    final right = Offset(centerX + arrowWidth / 2, arrowTopY);

    arrowPath.moveTo(arrowTip.dx, arrowTip.dy);
    arrowPath.lineTo(left.dx, left.dy);
    arrowPath.lineTo(right.dx, right.dy);
    arrowPath.close();

    canvas.drawPath(arrowPath, fillPaint);

    // Line starts 1px below the arrow tip
    final lineStartY = arrowBottomY + gap;

    canvas.drawLine(
      Offset(centerX, lineStartY),
      Offset(centerX, size.height),
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant DownwardArrowLinePainter oldDelegate) {
    return oldDelegate.color != color || oldDelegate.topPadding != topPadding;
  }
}
