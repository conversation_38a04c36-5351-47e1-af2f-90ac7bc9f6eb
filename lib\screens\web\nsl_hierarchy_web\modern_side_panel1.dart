import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/level_config1.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/models/nsl_heirarchy_model1.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/models/nsl_tree_side_details_new.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/models/metric_info_model.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/nsl_hierarchy_provider.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/services/dummy_consolidated_service.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';

class ModernSidePanel1 extends StatelessWidget {
  final VoidCallback onClose;
  final double? width;
  final NSLHierarchyData1? nodeData;
  final NslTreeSidePanel? nodeDetails;
  final bool? isLoadingNodeDetails;
  final MetricsInfo? nodeTransactions;
  final bool? isLoadingTransactions;
  final Map<String, dynamic>? dateRange;
  final Function(String)? onArrowTap;
  final VoidCallback? onGoLoPanelClosed;
  final int? clearSelectionTrigger;
  final Function(DateTime from, DateTime to)? onDateRangeChanged;

  const ModernSidePanel1({
    super.key,
    required this.onClose,
    this.width,
    this.nodeData,
    this.nodeDetails,
    this.isLoadingNodeDetails,
    this.nodeTransactions,
    this.isLoadingTransactions,
    this.dateRange,
    this.onArrowTap,
    this.onGoLoPanelClosed,
    this.clearSelectionTrigger,
    this.onDateRangeChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<NslHierarchyProvider>(
      builder: (context, provider, child) {
        return _ModernSidePanelView(
          onClose: onClose,
          width: width,
          nodeData: nodeData,
          nodeDetails: nodeDetails,
          isLoadingNodeDetails: isLoadingNodeDetails,
          nodeTransactions: nodeTransactions,
          isLoadingTransactions: isLoadingTransactions,
          dateRange: dateRange,
          onArrowTap: onArrowTap,
          onGoLoPanelClosed: onGoLoPanelClosed,
          clearSelectionTrigger: clearSelectionTrigger,
          onDateRangeChanged: onDateRangeChanged,
          provider: provider,
        );
      },
    );
  }
}

class _ModernSidePanelView extends StatefulWidget {
  final VoidCallback onClose;
  final double? width;
  final NSLHierarchyData1? nodeData;
  final NslTreeSidePanel? nodeDetails;
  final bool? isLoadingNodeDetails;
  final MetricsInfo? nodeTransactions;
  final bool? isLoadingTransactions;
  final Map<String, dynamic>? dateRange;
  final Function(String)? onArrowTap;
  final VoidCallback? onGoLoPanelClosed;
  final int? clearSelectionTrigger;
  final Function(DateTime from, DateTime to)? onDateRangeChanged;
  final NslHierarchyProvider provider;

  const _ModernSidePanelView({
    required this.onClose,
    this.width,
    this.nodeData,
    this.nodeDetails,
    this.isLoadingNodeDetails,
    this.nodeTransactions,
    this.isLoadingTransactions,
    this.dateRange,
    this.onArrowTap,
    this.onGoLoPanelClosed,
    this.clearSelectionTrigger,
    this.onDateRangeChanged,
    required this.provider,
  });

  @override
  State<_ModernSidePanelView> createState() => _ModernSidePanelViewState();
}

class _ModernSidePanelViewState extends State<_ModernSidePanelView>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int _selectedTabIndex = 0;
  String? _selectedMetricType;
  DateTime? _selectedFromDate;
  DateTime? _selectedToDate;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(() {
      if (mounted) {
        setState(() {
          _selectedTabIndex = _tabController.index;
        });
      }
    });
  }

  @override
  void didUpdateWidget(_ModernSidePanelView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.clearSelectionTrigger != oldWidget.clearSelectionTrigger) {
      if (mounted) {
        setState(() {
          _selectedMetricType = null;
        });
      }
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, constraints) {
      return Stack(
        clipBehavior: Clip.none,
        children: [
          Container(
            width: constraints.maxWidth,
            height: MediaQuery.of(context).size.height,
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border(
                left: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
            ),
            child: Column(
              children: [
                _buildHeader(),
                _buildTabNavigation(),
                Expanded(
                  child: _buildTabContent(),
                ),
              ],
            ),
          ),
          // Positioned(
          //   left: -10,
          //   top: 24,
          //   child: MouseRegion(
          //     cursor: SystemMouseCursors.click,
          //     child: GestureDetector(
          //       onTap: widget.onClose,
          //       behavior: HitTestBehavior.opaque,
          //       child: Container(
          //         width: 24,
          //         height: 24,
          //         decoration: BoxDecoration(
          //           color: Colors.white,
          //           shape: BoxShape.circle,
          //           border: Border.all(color: Colors.grey.shade400, width: 1),
          //         ),
          //         child: Icon(
          //           Icons.close,
          //           size: 12,
          //           color: Colors.black,
          //         ),
          //       ),
          //     ),
          //   ),
          // ),
        ],
      );
    });
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(AppSpacing.lg),
      decoration: BoxDecoration(
        color: Color(0XFFF1F3F4),
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200, width: 1),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  height: 48,
                  width: 48,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 12,
                  ),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white,
                  ),
                  child: Text(
                    widget.nodeData!.level,
                    style: FontManager.getCustomStyle(
                      fontSize: FontManager.s16,
                      fontWeight: FontManager.bold,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                SizedBox(height: AppSpacing.size6),
                Text(
                  widget.nodeData!.title,
                  style: FontManager.getCustomStyle(
                    fontSize: FontManager.s14,
                    fontWeight: FontManager.bold,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                ),
                SizedBox(height: AppSpacing.size10),
                Row(
                  children: [
                    InkWell(
                      onTap: () => _selectFromDate(context),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          border: Border.all(
                            color: Color(0XFFB4B4B4),
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.calendar_today,
                                size: 10, color: Colors.black),
                            SizedBox(width: 4),
                            Text(
                              _formatDate(
                                  _selectedFromDate?.toIso8601String() ??
                                      widget.dateRange?['from']),
                              style: FontManager.getCustomStyle(
                                fontSize: FontManager.s10,
                                fontWeight: FontManager.regular,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.black,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(width: AppSpacing.xxs),
                    Text(
                      'To',
                      style: FontManager.getCustomStyle(
                        fontSize: FontManager.s10,
                        fontWeight: FontManager.regular,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                    SizedBox(width: AppSpacing.xxs),
                    InkWell(
                      onTap: () => _selectToDate(context),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          border: Border.all(
                            color: Color(0XFFB4B4B4),
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.calendar_today,
                                size: 10, color: Colors.black),
                            SizedBox(width: 4),
                            Text(
                              _formatDate(
                                  _selectedToDate?.toIso8601String() ??
                                      widget.dateRange?['to']),
                              style: FontManager.getCustomStyle(
                                fontSize: FontManager.s10,
                                fontWeight: FontManager.regular,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.black,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Expanded(
            flex: 2,
            child: _buildDynamicMetricsTable(),
          ),
        ],
      ),
    );
  }

  Widget _buildTabNavigation() {
    return Container(
      padding: EdgeInsets.only(left: 7),
      decoration: BoxDecoration(
        color: Color(0XFFE4EDFF),
        border: Border(
          bottom: BorderSide(
            color: Color(0XFFE4EDFF),
          ),
        ),
      ),
      child: TabBar(
        tabAlignment: TabAlignment.start,
        controller: _tabController,
        isScrollable: true,
        dividerColor: Colors.transparent,
        labelPadding: EdgeInsets.symmetric(horizontal: 16.0),
        indicator: UnderlineTabIndicator(
          borderSide: BorderSide(color: Color(0XFF0058FF), width: 2.5),
          insets: EdgeInsets.only(bottom: 15),
        ),
        labelStyle: FontManager.getCustomStyle(
            fontSize: FontManager.s11,
            fontWeight: FontManager.bold,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black),
        unselectedLabelStyle: FontManager.getCustomStyle(
            fontSize: FontManager.s11,
            fontWeight: FontManager.regular,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black),
        tabs: const [
          Tab(text: 'Standalone'),
          Tab(text: 'Consolidated'),
        ],
      ),
    );
  }

  Widget _buildTabContent() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildStandaloneTab(),
        _buildConsolidatedTab(),
      ],
    );
  }

  Widget _buildStandaloneTab() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildFinancialCards(),
          SizedBox(height: AppSpacing.lg),
          _buildIncomeStatement(),
          SizedBox(height: AppSpacing.lg),
          _buildBalanceSheet(),
        ],
      ),
    );
  }

  Widget _buildFinancialCards() {
    return Padding(
      padding: EdgeInsets.only(
          top: AppSpacing.md, left: AppSpacing.lg, right: AppSpacing.lg),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildFinancialCard(
                  'Total Revenue',
                  '',
                  '',
                  Colors.green,
                  Icons.trending_up,
                ),
              ),
              SizedBox(width: AppSpacing.md),
              Expanded(
                child: _buildFinancialCard(
                  'Net Margin',
                  '',
                  '',
                  Colors.blue,
                  Icons.trending_up,
                ),
              ),
            ],
          ),
          SizedBox(height: AppSpacing.md),
          Row(
            children: [
              Expanded(
                child: _buildFinancialCard(
                  'Total Transactions',
                  '',
                  '',
                  Colors.orange,
                  Icons.trending_up,
                ),
              ),
              SizedBox(width: AppSpacing.md),
              Expanded(
                child: SizedBox(),
              )
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialCard(
    String title,
    String value,
    String trend,
    Color color,
    IconData icon,
  ) {
    String displayValue = 'N/A';
    String displayTrend = trend;
    
    if (widget.nodeTransactions?.result != null) {
      final transactionData = widget.nodeTransactions!.result!;
      
      switch (title) {
        case 'Total Revenue':
          if (transactionData.revenue != null) {
            displayValue = '\$${_formatNumber(transactionData.revenue!)}';
          }
          break;
        case 'Net Margin':
          if (transactionData.margin != null) {
            displayValue = '${transactionData.margin!.toStringAsFixed(1)}%';
          }
          break;
        case 'Total Transactions':
          if (transactionData.totalTransactions != null) {
            displayValue = _formatTransactionCount(transactionData.totalTransactions!);
          }
          break;
      }
    }
    
    if (widget.isLoadingTransactions == true && title != 'BET Efficiency') {
      return Container(
        padding: EdgeInsets.all(AppSpacing.size10),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: Color(0XFFB4B4B4)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: FontManager.getCustomStyle(
                    fontSize: FontManager.s14,
                    fontWeight: FontManager.regular,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                ),
                SizedBox(
                  width: 12,
                  height: 12,
                  child: CircularProgressIndicator(
                    strokeWidth: 1.5,
                    valueColor: AlwaysStoppedAnimation<Color>(color),
                  ),
                ),
              ],
            ),
            SizedBox(height: AppSpacing.sm),
            Text(
              displayTrend,
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s10,
                fontWeight: FontManager.regular,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ],
        ),
      );
    }

    final finalValue = displayValue != 'N/A' ? displayValue : value;
    
    return Container(
      padding: EdgeInsets.all(AppSpacing.size10),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Color(0XFFB4B4B4)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: FontManager.getCustomStyle(
                  fontSize: FontManager.s14,
                  fontWeight: FontManager.regular,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
              Text(
                finalValue,
                style: FontManager.getCustomStyle(
                  fontSize: FontManager.s13,
                  fontWeight: FontManager.bold,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
            ],
          ),
          SizedBox(height: AppSpacing.sm),
          Text(
            displayTrend,
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s10,
              fontWeight: FontManager.regular,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIncomeStatement() {
    return Container(
      margin: EdgeInsets.only(left: 8, right: 8),
      padding: EdgeInsets.symmetric(
        horizontal: AppSpacing.md,
        vertical: AppSpacing.sm,
      ),
      decoration: BoxDecoration(
        color: Color(0xFFF8F9FA),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Income Statement - Standalone',
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s14,
              fontWeight: FontManager.semiBold,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          Divider(color: Color(0xFFB4B4B4)),
          SizedBox(height: AppSpacing.size6),
          Column(
            children: [
              _buildIncomeStatementHeader(),
              ..._buildIncomeStatementRows(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildIncomeStatementHeader() {
    return Container(
      padding: EdgeInsets.only(
          left: AppSpacing.sm,
          bottom: AppSpacing.xxs,
          top: AppSpacing.sm,
          right: AppSpacing.sm),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              'Line Item',
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: FontManager.semiBold,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'Amount',
              textAlign: TextAlign.right,
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: FontManager.semiBold,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              '% of Revenue',
              textAlign: TextAlign.right,
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: FontManager.semiBold,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildIncomeStatementRows() {
    if (widget.isLoadingTransactions == true) {
      return [
        Container(
          padding: EdgeInsets.all(AppSpacing.lg),
          child: Center(
            child: CircularProgressIndicator(),
          ),
        ),
      ];
    }

    if (widget.nodeTransactions?.result?.pnl == null || 
        widget.nodeTransactions!.result!.pnl!.isEmpty) {
      return [
        Container(
          padding: EdgeInsets.all(AppSpacing.lg),
          child: Text(
            'No PnL data available',
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s12,
              fontWeight: FontManager.regular,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey.shade600,
            ),
          ),
        ),
      ];
    }

    final pnlData = widget.nodeTransactions!.result!.pnl!;
    List<Widget> rows = [];
    
    BalanceSheet? incomeSection;
    BalanceSheet? expenseSection;
    
    for (var section in pnlData) {
      if (section.name?.toLowerCase().contains('income') == true) {
        incomeSection = section;
      } else if (section.name?.toLowerCase().contains('expense') == true) {
        expenseSection = section;
      }
    }
    
    double totalIncome = 0.0;
    double totalExpense = 0.0;
    
    if (incomeSection != null) {
      totalIncome = incomeSection.sum ?? 0.0;
      
      rows.add(_buildIncomeStatementRow(
        'Income',
        '',
        '',
        isSubHeader: true,
      ));
      
      if (incomeSection.details != null) {
        for (var detail in incomeSection.details!) {
          final amount = detail.value ?? 0.0;
          final percentage = totalIncome > 0 ? (amount / totalIncome) * 100 : 0.0;
          
          rows.add(_buildIncomeStatementRow(
            detail.name ?? '',
            _formatCurrency(amount),
            '${percentage.toStringAsFixed(1)}%',
          ));
        }
      }
      
      rows.add(_buildIncomeStatementRow(
        'Total Income',
        _formatCurrency(totalIncome),
        '100%',
        isTotal: true,
      ));
    }
    
    if (expenseSection != null) {
      totalExpense = expenseSection.sum ?? 0.0;
      
      rows.add(_buildIncomeStatementRow(
        'Expense',
        '',
        '',
        isSubHeader: true,
      ));
      
      if (expenseSection.details != null) {
        for (var detail in expenseSection.details!) {
          final amount = detail.value ?? 0.0;
          final percentage = totalExpense > 0 ? (amount / totalExpense) * 100 : 0.0;
          
          rows.add(_buildIncomeStatementRow(
            detail.name ?? '',
            _formatCurrency(amount),
            '${percentage.toStringAsFixed(1)}%',
          ));
        }
      }
      
      rows.add(_buildIncomeStatementRow(
        'Total Expense',
        _formatCurrency(totalExpense),
        '100%',
        isTotal: true,
      ));
    }
    
    final netProfit = totalIncome - totalExpense;
    rows.add(_buildIncomeStatementRow(
      'Net Profit',
      _formatCurrency(netProfit),
      '',
      isFinal: true,
    ));
    
    return rows;
  }

  Widget _buildIncomeStatementRow(String item, String amount, String percentage,
      {bool isTotal = false, bool isFinal = false, bool isSubHeader = false}) {
    final fontWeight = (isTotal || isFinal || isSubHeader) 
        ? FontManager.bold 
        : FontManager.regular;
    final backgroundColor = isTotal
        ? Color(0xFFF0F0F0)
        : (isFinal ? Color(0xFFE8F5E8) : Colors.transparent);

    return Container(
      padding: EdgeInsets.all(AppSpacing.sm),
      decoration: BoxDecoration(
        color: backgroundColor,
      ),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              item,
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: fontWeight,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              amount,
              textAlign: TextAlign.right,
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: fontWeight,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              percentage,
              textAlign: TextAlign.right,
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: fontWeight,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBalanceSheet() {
    return Container(
      margin: EdgeInsets.only(left: 8, right: 8),
      padding: EdgeInsets.symmetric(
        horizontal: AppSpacing.md,
        vertical: AppSpacing.sm,
      ),
      decoration: BoxDecoration(
        color: Color(0xFFF8F9FA),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Balance Sheet - Standalone',
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s14,
              fontWeight: FontManager.semiBold,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          Divider(color: Color(0xFFB4B4B4)),
          SizedBox(height: AppSpacing.size6),
          Column(
            children: [
              _buildBalanceSheetHeader(),
              ..._buildBalanceSheetRows(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBalanceSheetHeader() {
    return Container(
      padding: EdgeInsets.only(
          left: AppSpacing.sm,
          bottom: AppSpacing.xxs,
          top: AppSpacing.sm,
          right: AppSpacing.sm),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              'Line Item',
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: FontManager.semiBold,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'Amount',
              textAlign: TextAlign.right,
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: FontManager.semiBold,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              '% of Revenue',
              textAlign: TextAlign.right,
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: FontManager.semiBold,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildBalanceSheetRows() {
    if (widget.isLoadingTransactions == true) {
      return [
        Container(
          padding: EdgeInsets.all(AppSpacing.lg),
          child: Center(
            child: CircularProgressIndicator(),
          ),
        ),
      ];
    }

    if (widget.nodeTransactions?.result?.balanceSheet == null || 
        widget.nodeTransactions!.result!.balanceSheet!.isEmpty) {
      return [
        Container(
          padding: EdgeInsets.all(AppSpacing.lg),
          child: Text(
            'No Balance Sheet data available',
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s12,
              fontWeight: FontManager.regular,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey.shade600,
            ),
          ),
        ),
      ];
    }

    final balanceSheetData = widget.nodeTransactions!.result!.balanceSheet!;
    List<Widget> rows = [];
    
    BalanceSheet? assetSection;
    BalanceSheet? liabilitySection;
    
    for (var section in balanceSheetData) {
      if (section.name?.toLowerCase().contains('asset') == true) {
        assetSection = section;
      } else if (section.name?.toLowerCase().contains('liability') == true) {
        liabilitySection = section;
      }
    }
    
    if (assetSection != null) {
      final assetTotal = assetSection.sum ?? 0.0;
      
      rows.add(_buildBalanceSheetRow(
        'Asset',
        '',
        '',
        isSubHeader: true,
      ));
      
      if (assetSection.details != null) {
        for (var detail in assetSection.details!) {
          final amount = detail.value ?? 0.0;
          final percentage = assetTotal > 0 ? (amount / assetTotal) * 100 : 0.0;
          
          rows.add(_buildBalanceSheetRow(
            detail.name ?? '',
            _formatCurrency(amount),
            '${percentage.toStringAsFixed(1)}%',
          ));
        }
      }
      
      rows.add(_buildBalanceSheetRow(
        'Asset Total',
        _formatCurrency(assetTotal),
        '100%',
        isTotal: true,
      ));
    }
    
    if (liabilitySection != null) {
      final liabilityTotal = liabilitySection.sum ?? 0.0;
      
      rows.add(_buildBalanceSheetRow(
        'Liability',
        '',
        '',
        isSubHeader: true,
      ));
      
      if (liabilitySection.details != null) {
        for (var detail in liabilitySection.details!) {
          final amount = detail.value ?? 0.0;
          final percentage = liabilityTotal > 0 ? (amount / liabilityTotal) * 100 : 0.0;
          
          rows.add(_buildBalanceSheetRow(
            detail.name ?? '',
            _formatCurrency(amount),
            '${percentage.toStringAsFixed(1)}%',
          ));
        }
      }
      
      rows.add(_buildBalanceSheetRow(
        'Liability Total',
        _formatCurrency(liabilityTotal),
        '100%',
        isTotal: true,
      ));
    }
    
    return rows;
  }

  Widget _buildBalanceSheetRow(String item, String amount, String percentage,
      {bool isTotal = false, bool isFinal = false, bool isSubHeader = false}) {
    final fontWeight = (isTotal || isFinal || isSubHeader) 
        ? FontManager.bold 
        : FontManager.regular;
    final backgroundColor = isTotal
        ? Color(0xFFF0F0F0)
        : (isFinal ? Color(0xFFE8F5E8) : Colors.transparent);

    return Container(
      padding: EdgeInsets.all(AppSpacing.sm),
      decoration: BoxDecoration(
        color: backgroundColor,
      ),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              item,
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: fontWeight,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              amount,
              textAlign: TextAlign.right,
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: fontWeight,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              percentage,
              textAlign: TextAlign.right,
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: fontWeight,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConsolidatedTab() {
    if (widget.nodeData?.id == null) {
      return SingleChildScrollView(
        padding: EdgeInsets.only(
            top: AppSpacing.md, left: AppSpacing.lg, right: AppSpacing.lg),
        child: Text(
          'No node data available',
          style: FontManager.getCustomStyle(
            fontSize: FontManager.s14,
            fontWeight: FontManager.regular,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.grey.shade600,
          ),
        ),
      );
    }

    return FutureBuilder<List<dynamic>>(
      future: DummyConsolidatedService.loadDummyData(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Center(
            child: CircularProgressIndicator(),
          );
        }

        if (snapshot.hasError) {
          return SingleChildScrollView(
            padding: EdgeInsets.only(
                top: AppSpacing.md, left: AppSpacing.lg, right: AppSpacing.lg),
            child: Text(
              'Error loading consolidated data: ${snapshot.error}',
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s14,
                fontWeight: FontManager.regular,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.red,
              ),
            ),
          );
        }

        final dataList = snapshot.data ?? [];
        Map<String, dynamic>? nodeData;
        
      
        
        // Find the node data by ID
        for (var item in dataList) {
          if (item is Map<String, dynamic> && item['id'] == widget.nodeData!.id) {
            nodeData = item;
            // print('Found node data: $nodeData');
            break;
          }
        }

        // if (nodeData == null) {
        //   print('Node data not found for ID: ${widget.nodeData!.id}');
        // }

        // Extract values from the consolidated data
        String totalGo = nodeData?['consolidated']?['total_gos'] ?? 'NOT_FOUND';
        String internalElimination = nodeData?['consolidated']?['internal_elimination'] ?? 'NOT_FOUND';
        String efficiency = nodeData?['consolidated']?['effeciency'] ?? 'NOT_FOUND';
        String teamCoordination = nodeData?['consolidated']?['team_coordination'] ?? 'NOT_FOUND';
        
        // print('Extracted values - Total Go: $totalGo, Internal Elimination: $internalElimination, Efficiency: $efficiency, Team Coordination: $teamCoordination');

        return SingleChildScrollView(
          padding: EdgeInsets.only(
              top: AppSpacing.md, left: AppSpacing.lg, right: AppSpacing.lg),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: _buildConsolidatedMetricCard(
                      'Total Go',
                      totalGo,
                    ),
                  ),
                  SizedBox(width: AppSpacing.sm),
                  Expanded(
                    child: _buildConsolidatedMetricCard(
                      'Internal Elimination',
                      internalElimination,
                    ),
                  ),
                ],
              ),
              SizedBox(height: AppSpacing.sm),
              Row(
                children: [
                  Expanded(
                    child: _buildConsolidatedMetricCard(
                      'Efficiency',
                      efficiency,
                    ),
                  ),
                  SizedBox(width: AppSpacing.sm),
                  Expanded(
                    child: _buildConsolidatedMetricCard(
                      'Team Coordination',
                      teamCoordination,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildConsolidatedMetricCard(String title, String value) {
    return Container(
      margin: EdgeInsets.only(bottom: AppSpacing.sm),
      padding: EdgeInsets.all(AppSpacing.size10),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Color(0XFFB4B4B4)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: FontManager.getCustomStyle(
                  fontSize: FontManager.s14,
                  fontWeight: FontManager.regular,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
              SizedBox(height: AppSpacing.lg),
              // Text(
              //   change,
              //   style: FontManager.getCustomStyle(
              //     fontSize: FontManager.s10,
              //     fontWeight: FontManager.regular,
              //     fontFamily: FontManager.fontFamilyTiemposText,
              //     color: Colors.black,
              //   ),
              // ),
            ],
          ),
          Text(
            value,
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s13,
              fontWeight: FontManager.bold,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
        ],
      ),
    );
  }
 
 

  // Widget _buildConsolidatedTab() {
  //   return SingleChildScrollView(
  //     padding: EdgeInsets.only(
  //         top: AppSpacing.md, left: AppSpacing.lg, right: AppSpacing.lg),
  //     child: Column(
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         Text(
  //           'Consolidated data not available',
  //           style: FontManager.getCustomStyle(
  //             fontSize: FontManager.s14,
  //             fontWeight: FontManager.regular,
  //             fontFamily: FontManager.fontFamilyTiemposText,
  //             color: Colors.grey.shade600,
  //           ),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  Widget _buildDynamicMetricsTable() {
    final level = widget.nodeData?.level ?? 'M4';
    final config = LevelConfigurations.getConfigOrDefault(level);
    final accessor = DynamicDataAccessor(widget.nodeData);

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Color(0XFFB4B4B4)),
      ),
      child: Column(
        children: config.metricsLayout.map<Widget>((layoutItem) {
          if (layoutItem is MetricRow) {
            return _buildDynamicMetricRow(layoutItem, accessor);
          } else if (layoutItem is FinancialRow) {
            return _buildDynamicFinancialRow(layoutItem, accessor);
          }
          return Container();
        }).toList(),
      ),
    );
  }

  Widget _buildDynamicMetricRow(MetricRow metricRow, DynamicDataAccessor accessor) {
    final isLeftSelected = _selectedMetricType == metricRow.left.label;
    final isRightSelected = _selectedMetricType == metricRow.right.label;

    return Row(
      children: [
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              color: isLeftSelected ? Color(0xFF0058FF) : Colors.white,
              border: Border(
                right: BorderSide(color: Color(0XFFB4B4B4)),
                bottom: BorderSide(color: Color(0XFFB4B4B4)),
              ),
            ),
            child: InkWell(
              onTap: _shouldShowArrow(metricRow.left.label)
                  ? () => _onArrowTapped(metricRow.left.label)
                  : null,
              child: Container(
                padding: EdgeInsets.all(AppSpacing.size10),
                child: Row(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          metricRow.left.label,
                          style: FontManager.getCustomStyle(
                            fontSize: FontManager.s10,
                            fontWeight: FontManager.regular,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: isLeftSelected ? Colors.white : Colors.black,
                          ),
                        ),
                        SizedBox(width: 5),
                        if (_shouldShowArrow(metricRow.left.label))
                          Icon(
                            Icons.arrow_forward,
                            size: 12,
                            color: isLeftSelected ? Colors.white : Colors.black,
                          ),
                      ],
                    ),
                    Spacer(),
                    _buildLoadingOrValue(
                      metricRow.left.label,
                      accessor.formatValue(
                        accessor.getMetricValue(metricRow.left.dataPath),
                        metricRow.left.formatter,
                      ),
                      isLeftSelected ? Colors.white : Colors.black,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              color: isRightSelected ? Color(0xFF0058FF) : Colors.white,
              border: Border(
                bottom: BorderSide(color: Color(0XFFB4B4B4)),
              ),
            ),
            child: InkWell(
              onTap: _shouldShowArrow(metricRow.right.label)
                  ? () => _onArrowTapped(metricRow.right.label)
                  : null,
              child: Container(
                padding: EdgeInsets.all(AppSpacing.size10),
                child: Row(
                  children: [
                    Row(
                      children: [
                        Text(
                          metricRow.right.label,
                          style: FontManager.getCustomStyle(
                            fontSize: FontManager.s10,
                            fontWeight: FontManager.regular,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: isRightSelected ? Colors.white : Colors.black,
                          ),
                        ),
                        SizedBox(width: 5),
                        if (_shouldShowArrow(metricRow.right.label))
                          Icon(
                            Icons.arrow_forward,
                            size: 12,
                            color: isRightSelected ? Colors.white : Colors.black,
                          ),
                      ],
                    ),
                    Spacer(),
                    _buildLoadingOrValue(
                      metricRow.right.label,
                      accessor.formatValue(
                        accessor.getMetricValue(metricRow.right.dataPath),
                        metricRow.right.formatter,
                      ),
                      isRightSelected ? Colors.white : Colors.black,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDynamicFinancialRow(FinancialRow financialRow, DynamicDataAccessor accessor) {
    return Row(
      children: financialRow.cells.asMap().entries.map((entry) {
        final index = entry.key;
        final cell = entry.value;
        final isLast = index == financialRow.cells.length - 1;

        return Expanded(
          child: Container(
            decoration: BoxDecoration(
              color: Color(0XFFE7F8F5),
              border: Border(
                right: isLast
                    ? BorderSide.none
                    : BorderSide(color: Color(0XFFB4B4B4)),
              ),
            ),
            padding: EdgeInsets.all(AppSpacing.size10),
            child: Row(
              children: [
                Text(
                  cell.label,
                  style: FontManager.getCustomStyle(
                    fontSize: FontManager.s8,
                    fontWeight: FontManager.regular,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                ),
                Spacer(),
                _buildFinancialValue(cell),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildLoadingOrValue(String label, String defaultValue, Color textColor) {
    if (label.toLowerCase().contains('gos') && widget.nodeDetails?.result?.goCount != null) {
      return Text(
        widget.nodeDetails!.result!.goCount.toString(),
        style: FontManager.getCustomStyle(
          fontSize: FontManager.s10,
          fontWeight: FontManager.bold,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: textColor,
        ),
      );
    } else if (label.toLowerCase().contains('los') && widget.nodeDetails?.result?.loCount != null) {
      return Text(
        widget.nodeDetails!.result!.loCount.toString(),
        style: FontManager.getCustomStyle(
          fontSize: FontManager.s10,
          fontWeight: FontManager.bold,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: textColor,
        ),
      );
    } else if (label.toLowerCase().contains('transaction') && widget.nodeTransactions?.result?.totalTransactions != null) {
      return Text(
        _formatTransactionCount(widget.nodeTransactions!.result!.totalTransactions!),
        style: FontManager.getCustomStyle(
          fontSize: FontManager.s10,
          fontWeight: FontManager.bold,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: textColor,
        ),
      );
    } else if ((label.toLowerCase().contains('gos') || label.toLowerCase().contains('los')) && 
               widget.isLoadingNodeDetails == true) {
      return SizedBox(
        width: 12,
        height: 12,
        child: CircularProgressIndicator(
          strokeWidth: 1.5,
          valueColor: AlwaysStoppedAnimation<Color>(textColor),
        ),
      );
    } else if (label.toLowerCase().contains('transaction') && widget.isLoadingTransactions == true) {
      return SizedBox(
        width: 12,
        height: 12,
        child: CircularProgressIndicator(
          strokeWidth: 1.5,
          valueColor: AlwaysStoppedAnimation<Color>(textColor),
        ),
      );
    } else {
      return Text(
        defaultValue,
        style: FontManager.getCustomStyle(
          fontSize: FontManager.s10,
          fontWeight: FontManager.bold,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: textColor,
        ),
      );
    }
  }

  Widget _buildFinancialValue(MetricCell cell) {
    if (widget.isLoadingTransactions == true) {
      return SizedBox(
        width: 12,
        height: 12,
        child: CircularProgressIndicator(
          strokeWidth: 1.5,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
        ),
      );
    }
    
    String value = 'N/A';
    
    if (widget.nodeTransactions?.result != null) {
      final transactionData = widget.nodeTransactions!.result!;
      
      switch (cell.dataPath) {
        case 'revenue':
          if (transactionData.revenue != null) {
            value = _formatNumber(transactionData.revenue!);
          }
          break;
        case 'cost':
          if (transactionData.cost != null) {
            value = _formatNumber(transactionData.cost!);
          }
          break;
        case 'margin':
          if (transactionData.margin != null) {
            value = '${transactionData.margin!.toStringAsFixed(1)}%';
          }
          break;
      }
    }
    
    return Text(
      value,
      style: FontManager.getCustomStyle(
        fontSize: FontManager.s10,
        fontWeight: FontManager.bold,
        fontFamily: FontManager.fontFamilyTiemposText,
        color: Colors.black,
      ),
    );
  }

  void _onArrowTapped(String metricType) {
    if (mounted) {
      setState(() {
        _selectedMetricType = metricType;
      });
    }
    widget.onArrowTap?.call(metricType);
  }

  bool _shouldShowArrow(String label) {
    return label.toLowerCase().contains('go') ||
        label.toLowerCase().contains('lo') &&
         !label.toLowerCase().contains('m1') &&
         !label.toLowerCase().contains('employee') &&
         !label.toLowerCase().contains('local')
         ;
  }

  String _formatNumber(num value) {
    if (value >= 1000000) {
      return '${(value / 1000000).toStringAsFixed(1)}M';
    } else if (value >= 1000) {
      return '${(value / 1000).toStringAsFixed(1)}K';
    } else {
      return value.toStringAsFixed(0);
    }
  }

  String _formatTransactionCount(int value) {
    if (value >= 1000000) {
      return '${(value / 1000000).toStringAsFixed(1)}M';
    } else if (value >= 1000) {
      return '${(value / 1000).toStringAsFixed(1)}K';
    } else {
      return value.toString();
    }
  }

  String _formatCurrency(double value) {
    if (value >= 1000000) {
      return '\$${(value / 1000000).toStringAsFixed(1)}M';
    } else if (value >= 1000) {
      return '\$${(value / 1000).toStringAsFixed(1)}K';
    } else {
      return '\$${value.toStringAsFixed(0)}';
    }
  }

  String _formatDate(String? isoDate) {
    if (isoDate == null) return 'DD/MM/YY';
    try {
      final date = DateTime.parse(isoDate);
      return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year.toString().substring(2)}';
    } catch (e) {
      return 'DD/MM/YY';
    }
  }

  Future<void> _selectFromDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedFromDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );
    if (picked != null && picked != _selectedFromDate) {
      if (mounted) {
        setState(() {
          _selectedFromDate = picked;
        });
      }
      
      if (_selectedToDate != null && widget.onDateRangeChanged != null) {
        widget.onDateRangeChanged!(picked, _selectedToDate!);
      }
    }
  }

  Future<void> _selectToDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedToDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );
    if (picked != null && picked != _selectedToDate) {
      if (mounted) {
        setState(() {
          _selectedToDate = picked;
        });
      }
      
      if (_selectedFromDate != null && widget.onDateRangeChanged != null) {
        widget.onDateRangeChanged!(_selectedFromDate!, picked);
      }
    }
  }
}
