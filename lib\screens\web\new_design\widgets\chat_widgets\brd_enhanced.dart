import 'package:flutter/material.dart';
import 'package:expansion_tile_group/expansion_tile_group.dart';
import 'package:nsl/theme/app_colors.dart';
import 'package:nsl/utils/font_manager.dart';

void main() {
  runApp(MaterialApp(
      home: EnhancedCollapsibleViewer(
    jsonData: _getExpandData(),
    showAppBar: false,
  )));
}

class UltraOptimizedJSONParser {
  // Lightweight data structure instead of widgets
  static List<RenderItem> parseToItems(Map<String, dynamic> jsonData) {
    Map<String, dynamic> rootData = _findRootData(jsonData);
    return _parseData(rootData, 0);
  }

  static Map<String, dynamic> _findRootData(Map<String, dynamic> jsonData) {
    if (jsonData.containsKey('complete_json') &&
        jsonData['complete_json'] is Map) {
      return jsonData['complete_json'];
    }
    if (jsonData.containsKey('data') && jsonData['data'] is Map) {
      return jsonData['data'];
    }
    return jsonData;
  }

  static List<RenderItem> _parseData(Map<String, dynamic> data, int level) {
    List<RenderItem> items = [];
    List<String> keys = data.keys.toList();

    // if (keys.length > 1) keys.sort();

    for (int i = 0; i < keys.length; i++) {
      String key = keys[i];
      dynamic value = data[key];

      items.addAll(_parseKeyValue(key, value, level));

      if (i < keys.length - 1) {
        items.add(RenderItem.spacing(level == 0 ? 20.0 : 16.0));
      }
    }

    return items;
  }

  static List<RenderItem> _parseKeyValue(String key, dynamic value, int level) {
    List<RenderItem> items = [];

    if (value == null) return items;

    if (value is Map<String, dynamic>) {
      items.add(RenderItem.header(_formatKey(key), level + 1, level));
      items.add(RenderItem.spacing(8.0));
      items.addAll(_parseData(value, level + 1));
    } else if (value is List) {
      items.add(RenderItem.header(_formatKey(key), level + 2, level));
      items.add(RenderItem.spacing(6.0));

      for (int i = 0; i < value.length; i++) {
        dynamic item = value[i];
        if (item is Map<String, dynamic>) {
          String title = _findItemTitle(item) ?? 'Item ${i + 1}';
          items.add(RenderItem.subHeader(title, level + 1));
          items.addAll(_parseData(item, level + 2));

          if (i < value.length - 1) {
            items.add(RenderItem.spacing(6.0));
          }
        } else {
          // Handle individual list items as bullet points
          String text = item.toString();
          items.add(RenderItem.bullet('${i + 1}. $text', level + 1));
        }
      }
    } else {
      items.add(RenderItem.keyValue(key, value.toString(), level));
      items.add(RenderItem.spacing(2.0));
    }

    return items;
  }

  static String? _findItemTitle(Map<String, dynamic> item) {
    if (item.containsKey('name')) return item['name']?.toString();
    if (item.containsKey('title')) return item['title']?.toString();
    if (item.containsKey('role')) return item['role']?.toString();
    return null;
  }

  static String _formatKey(String key) {
    return key
        .replaceAll('_', ' ')
        .split(' ')
        .map(
          (word) => word.isNotEmpty
              ? '${word[0].toUpperCase()}${word.substring(1).toLowerCase()}'
              : '',
        )
        .join(' ');
  }

  static String _formatList(List<dynamic> list) {
    return list.take(5).map((e) => e.toString()).join(', ') +
        (list.length > 5 ? '... (${list.length} items)' : '');
  }
}

// Lightweight data structure
class RenderItem {
  final RenderType type;
  final String text;
  final int headerLevel;
  final int indentLevel;
  final double spacing;
  final String? key;
  final String? value;

  RenderItem._(
    this.type,
    this.text,
    this.headerLevel,
    this.indentLevel,
    this.spacing,
    this.key,
    this.value,
  );

  factory RenderItem.header(String text, int level, int indent) =>
      RenderItem._(RenderType.header, text, level, indent, 0, null, null);

  factory RenderItem.subHeader(String text, int indent) =>
      RenderItem._(RenderType.subHeader, text, 4, indent, 0, null, null);

  factory RenderItem.keyValue(String key, String value, int indent) =>
      RenderItem._(RenderType.keyValue, '', 0, indent, 0, key, value);

  factory RenderItem.bullet(String text, int indent) =>
      RenderItem._(RenderType.bullet, text, 0, indent, 0, null, null);

  factory RenderItem.text(String text, int indent) =>
      RenderItem._(RenderType.text, text, 0, indent, 0, null, null);

  factory RenderItem.spacing(double height) =>
      RenderItem._(RenderType.spacing, '', 0, 0, height, null, null);
}

enum RenderType { header, subHeader, keyValue, text, bullet, spacing }

class UltraOptimizedDisplayWidget extends StatefulWidget {
  final Map<String, dynamic> jsonData;
  final bool showAppBar;
  final EdgeInsetsGeometry? padding;

  const UltraOptimizedDisplayWidget({
    Key? key,
    required this.jsonData,
    this.showAppBar = true,
    this.padding,
  }) : super(key: key);

  @override
  State<UltraOptimizedDisplayWidget> createState() =>
      _UltraOptimizedDisplayWidgetState();
}

class _UltraOptimizedDisplayWidgetState
    extends State<UltraOptimizedDisplayWidget> {
  List<RenderItem> items = [];
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    // Parse immediately - no async needed for fast parsing
    items = UltraOptimizedJSONParser.parseToItems(widget.jsonData);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _refresh() {
    setState(() {
      items = UltraOptimizedJSONParser.parseToItems(widget.jsonData);
    });
  }

  @override
  Widget build(BuildContext context) {
    Widget content = ListView.builder(
      controller: _scrollController,
      padding: widget.padding ?? const EdgeInsets.all(24.0),
      itemCount: items.length,
      itemBuilder: (context, index) => _buildItem(items[index]),
    );

    if (widget.showAppBar) {
      return Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          title: const Text('Document Viewer'),
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          elevation: 1,
          actions: [
            IconButton(icon: const Icon(Icons.refresh), onPressed: _refresh),
          ],
        ),
        body: content,
        floatingActionButton: FloatingActionButton.small(
          onPressed: () => _scrollController.animateTo(
            0,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          ),
          backgroundColor: Colors.black,
          child: const Icon(Icons.keyboard_arrow_up, color: Colors.white),
        ),
      );
    }

    return Container(color: Colors.white, child: content);
  }

  Widget _buildItem(RenderItem item) {
    switch (item.type) {
      case RenderType.spacing:
        return SizedBox(height: item.spacing);

      case RenderType.header:
        return Container(
          margin: EdgeInsets.only(left: item.indentLevel * 24.0),
          child: Text(item.text, style: _getHeaderStyle(item.headerLevel)),
        );

      case RenderType.subHeader:
        return Container(
          margin: EdgeInsets.only(left: item.indentLevel * 24.0),
          child: Text(
            item.text,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
        );

      case RenderType.keyValue:
        return Container(
          margin: EdgeInsets.only(left: item.indentLevel * 24.0),
          child: RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: '${UltraOptimizedJSONParser._formatKey(item.key!)}: ',
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                    fontSize: 14,
                  ),
                ),
                TextSpan(
                  text: item.value!,
                  style: const TextStyle(color: Colors.black, fontSize: 14),
                ),
              ],
            ),
          ),
        );

      case RenderType.bullet:
        return Container(
          margin: EdgeInsets.only(left: item.indentLevel * 24.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // const Text(
              //   '• ',
              //   style: TextStyle(
              //     color: Colors.black,
              //     fontSize: 14,
              //     fontWeight: FontWeight.bold,
              //   ),
              // ),
              Expanded(
                child: Text(
                  item.text,
                  style: const TextStyle(color: Colors.black, fontSize: 14),
                ),
              ),
            ],
          ),
        );

      case RenderType.text:
        return Container(
          margin: EdgeInsets.only(left: item.indentLevel * 24.0),
          child: Text(
            item.text,
            style: const TextStyle(color: Colors.black, fontSize: 14),
          ),
        );
    }
  }

  TextStyle _getHeaderStyle(int level) {
    switch (level) {
      case 1:
        return const TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.bold,
          color: Colors.black,
        );
      case 2:
        return const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.black,
        );
      case 3:
        return const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Colors.black,
        );
      case 4:
        return const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: Colors.black,
        );
      default:
        return const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: Colors.black,
        );
    }
  }
}

// Ultra-fast usage examples
class UltraOptimizedExamples extends StatelessWidget {
  const UltraOptimizedExamples({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('Ultra-Optimized Display'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.flash_on,
                        color: Colors.green.shade700,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Ultra Performance Optimizations:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: Colors.green.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  const Text(
                    '⚡ Lightweight data structures (no heavy widgets)',
                    style: TextStyle(color: Colors.black87),
                  ),
                  const Text(
                    '⚡ ListView.builder for virtual scrolling',
                    style: TextStyle(color: Colors.black87),
                  ),
                  const Text(
                    '⚡ Minimal object creation during parsing',
                    style: TextStyle(color: Colors.black87),
                  ),
                  const Text(
                    '⚡ No async operations - instant parsing',
                    style: TextStyle(color: Colors.black87),
                  ),
                  const Text(
                    '⚡ Optimized string operations',
                    style: TextStyle(color: Colors.black87),
                  ),
                  const Text(
                    '⚡ Memory-efficient rendering',
                    style: TextStyle(color: Colors.black87),
                  ),
                  const Text(
                    '⚡ Proper bullet point formatting',
                    style: TextStyle(color: Colors.black87),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              onPressed: () =>
                  _testPerformance(context, 'Small', _getSampleData1()),
              child: const Text('Small Document (Should be instant)'),
            ),
            const SizedBox(height: 12),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              onPressed: () =>
                  _testPerformance(context, 'Medium', _getMediumData()),
              child: const Text('Medium Document (< 100ms)'),
            ),
            const SizedBox(height: 12),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              onPressed: () =>
                  _testPerformance(context, 'Large', _getLargeData()),
              child: const Text('Large Document (< 500ms)'),
            ),
            const SizedBox(height: 12),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              onPressed: () =>
                  _testPerformance(context, 'Deep Nesting', _getDeepData()),
              child: const Text('Deep Nesting (< 200ms)'),
            ),
          ],
        ),
      ),
    );
  }

  void _testPerformance(
    BuildContext context,
    String label,
    Map<String, dynamic> data,
  ) {
    Stopwatch stopwatch = Stopwatch()..start();

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) {
          stopwatch.stop();

          return Scaffold(
            appBar: AppBar(
              title: Text('$label - ${stopwatch.elapsedMilliseconds}ms'),
              backgroundColor: Colors.white,
              foregroundColor: Colors.black,
            ),
            body: UltraOptimizedDisplayWidget(
              jsonData: data,
              showAppBar: false,
            ),
          );
        },
      ),
    );
  }

  Map<String, dynamic> _getSmallData() {
    return {
      "user_info": {"name": "John Doe", "email": "<EMAIL>", "age": 30},
      "settings": {"theme": "dark", "notifications": true},
    };
  }

  Map<String, dynamic> _getMediumData() {
    return {
      "complete_json": {
        "section_1": {
          "metadata": {"type": "test", "version": "1.0"},
          "items": List.generate(
            50,
            (i) => {
              "id": i,
              "name": "Item $i",
              "data": {"value": i * 2, "active": i % 2 == 0},
            },
          ),
        },
        "section_2": {
          "users": List.generate(
            25,
            (i) => {
              "user_id": i,
              "username": "user$i",
              "profile": {
                "email": "user$<EMAIL>",
                "settings": {"theme": i % 2 == 0 ? "dark" : "light"},
              },
            },
          ),
        },
      },
    };
  }

  Map<String, dynamic> _getLargeData() {
    Map<String, dynamic> data = {"large_test": {}};

    // Generate 20 sections with 100 items each
    for (int s = 1; s <= 20; s++) {
      data["large_test"]["section_$s"] = {
        "title": "Section $s",
        "items": List.generate(
          100,
          (i) => {
            "id": "${s}_$i",
            "name": "Item $i",
            "value": i,
            "metadata": {"section": s, "index": i},
          },
        ),
      };
    }

    return data;
  }

  Map<String, dynamic> _getDeepData() {
    Map<String, dynamic> current = {};
    Map<String, dynamic> root = {"deep_test": current};

    // Create 15 levels deep
    for (int i = 1; i <= 15; i++) {
      current["level_$i"] = {
        "data": "Level $i data",
        "items": ["item1", "item2", "item3"],
      };
      if (i < 15) {
        current["level_$i"]["nested"] = {};
        current = current["level_$i"]["nested"];
      }
    }

    return root;
  }

  Map<String, dynamic> _getSampleData1() {
    return {
      "complete_json": {
        "section_1_user_persona_and_solution_archetype": {
          "solution_context": {
            "business_goals": [],
            "compliance_requirements": [],
            "success_metrics": [
              "Client Retention Rate",
              "Project Profit Margin",
              "Campaign ROI",
              "Client Satisfaction Score (CSAT)",
              "Lead Conversion Rate",
              "Average Project Timeline",
              "Resource Utilization Rate",
            ],
          },
          "solution_metadata": {
            "complexity_level": 5,
            "industry": "digital_agency",
            "region": "north_america",
            "solution_archetype": "standard",
            "solution_type": "crm",
            "team_size": "medium",
          },
          "user_personas": [
            {
              "description":
                  "Manages client relationships and oversees digital marketing campaigns for enterprise clients",
              "goals": ["Efficiently perform Digital Account Manager duties"],
              "needs": [
                "client relationship management",
                "campaign oversight",
                "revenue growth",
                "client strategy planning",
              ],
              "pain_points": ["Manual processes", "Lack of integration"],
              "role": "Digital Account Manager",
              "technical_proficiency": "medium",
            },
            {
              "description":
                  "Oversees design team and ensures creative deliverables meet client expectations",
              "goals": ["Efficiently perform Creative Lead duties"],
              "needs": [
                "creative direction",
                "design review",
                "brand compliance",
                "team management",
              ],
              "pain_points": ["Manual processes", "Lack of integration"],
              "role": "Creative Lead",
              "technical_proficiency": "medium",
            },
            {
              "description":
                  "Coordinates digital marketing projects and ensures timely delivery",
              "goals": ["Efficiently perform Project Manager duties"],
              "needs": [
                "project planning",
                "resource allocation",
                "timeline management",
                "stakeholder communication",
              ],
              "pain_points": ["Manual processes", "Lack of integration"],
              "role": "Project Manager",
              "technical_proficiency": "medium",
            },
          ],
        },
        "section_2_organization_information": {
          "organizational_context": {
            "industry": "digital_agency",
            "region": "north_america",
            "scale": "medium",
          },
          "roles": [
            {
              "authority_level": "senior",
              "auto_generated": true,
              "department": "Client Services",
              "description":
                  "Manages client relationships and oversees digital marketing campaigns for enterprise clients",
              "domain_specific": true,
              "key_functions": [
                "client relationship management",
                "campaign oversight",
                "revenue growth",
                "client strategy planning",
              ],
              "name": "Digital Account Manager",
              "permissions": [
                "client data access",
                "campaign management",
                "budget approval",
                "client reporting",
              ],
              "reports_to": "Operations Manager",
              "source": "llm_generation",
            },
            {
              "authority_level": "manager",
              "auto_generated": true,
              "department": "Creative",
              "description":
                  "Oversees design team and ensures creative deliverables meet client expectations",
              "domain_specific": true,
              "key_functions": [
                "creative direction",
                "design review",
                "brand compliance",
                "team management",
              ],
              "name": "Creative Lead",
              "permissions": [
                "asset library access",
                "creative approval",
                "resource allocation",
                "template management",
              ],
              "reports_to": "Operations Manager",
              "source": "llm_generation",
            },
            {
              "authority_level": "senior",
              "auto_generated": true,
              "department": "Project Management",
              "description":
                  "Coordinates digital marketing projects and ensures timely delivery",
              "domain_specific": true,
              "key_functions": [
                "project planning",
                "resource allocation",
                "timeline management",
                "stakeholder communication",
              ],
              "name": "Project Manager",
              "permissions": [
                "project dashboard access",
                "task assignment",
                "timeline management",
                "resource scheduling",
              ],
              "reports_to": "Operations Manager",
              "source": "llm_generation",
            },
            {
              "authority_level": "manager",
              "auto_generated": true,
              "department": "Analytics",
              "description":
                  "Leads data analysis and performance reporting for digital campaigns",
              "domain_specific": true,
              "key_functions": [
                "performance analysis",
                "reporting automation",
                "insights generation",
                "data strategy",
              ],
              "name": "Analytics Manager",
              "permissions": [
                "analytics tools access",
                "data export",
                "report creation",
                "dashboard configuration",
              ],
              "reports_to": "Operations Manager",
              "source": "llm_generation",
            },
            {
              "authority_level": "executive",
              "auto_generated": true,
              "department": "Operations",
              "description":
                  "Oversees agency operations and ensures efficient workflow across departments",
              "domain_specific": true,
              "key_functions": [
                "strategic planning",
                "resource optimization",
                "process improvement",
                "department coordination",
              ],
              "name": "Operations Manager",
              "permissions": [
                "system admin",
                "user management",
                "workflow configuration",
                "full system access",
              ],
              "reports_to": null,
              "source": "llm_generation",
            },
            {
              "authority_level": "senior",
              "auto_generated": true,
              "department": "Marketing",
              "description":
                  "Executes and optimizes digital marketing campaigns",
              "domain_specific": true,
              "key_functions": [
                "campaign execution",
                "performance optimization",
                "content coordination",
                "campaign analytics",
              ],
              "name": "Marketing Campaign Specialist",
              "permissions": [
                "campaign tools access",
                "content management",
                "performance tracking",
                "reporting tools",
              ],
              "reports_to": "Marketing Manager",
              "source": "llm_generation",
            },
            {
              "authority_level": "individual",
              "auto_generated": false,
              "department": "General",
              "description":
                  "Manages chief technology officer (cto) responsibilities and strategic initiatives",
              "domain_specific": true,
              "key_functions": [
                "strategic planning",
                "team coordination",
                "process improvement",
                "stakeholder management",
              ],
              "name": "Chief Technology Officer (Cto)",
              "permissions": ["full_access", "admin_rights"],
              "reports_to": "Agency Owner",
              "source": "user_modification",
            },
          ],
          "stakeholders": {
            "administrators": [],
            "decision_makers": [
              "Creative Lead",
              "Analytics Manager",
              "Operations Manager",
            ],
            "primary_users": [
              "Digital Account Manager",
              "Project Manager",
              "Marketing Campaign Specialist",
              "Chief Technology Officer (Cto)",
            ],
          },
          "team_structure": {
            "departments": [
              "Creative",
              "Marketing",
              "Analytics",
              "Operations",
              "Client Services",
              "Project Management",
              "General",
            ],
            "reporting_structure": {
              "Analytics Manager": "Operations Manager",
              "Chief Technology Officer (Cto)": "Agency Owner",
              "Creative Lead": "Operations Manager",
              "Digital Account Manager": "Operations Manager",
              "Marketing Campaign Specialist": "Marketing Manager",
              "Project Manager": "Operations Manager",
            },
            "total_size": 16,
          },
        },
        "section_3_entities_and_attributes": {
          "core_business_entities": [
            {
              "attributes": [
                {
                  "description": "Unique identifier",
                  "name": "id",
                  "required": true,
                  "type": "string",
                },
                {
                  "description": "Official company name",
                  "name": "company_name",
                  "required": true,
                  "type": "string",
                },
                {
                  "description": "Client's industry sector",
                  "name": "industry",
                  "required": true,
                  "type": "string",
                },
                {
                  "description": "Client's annual revenue",
                  "name": "annual_revenue",
                  "required": false,
                  "type": "decimal",
                },
                {
                  "description": "Assigned account manager",
                  "name": "account_manager_id",
                  "required": true,
                  "type": "string",
                },
                {
                  "description": "Client's primary location",
                  "name": "location",
                  "required": true,
                  "type": "string",
                },
                {
                  "description": "Client onboarding date",
                  "name": "onboarding_date",
                  "required": true,
                  "type": "datetime",
                },
              ],
              "auto_generated": true,
              "business_rules": [
                "Must have at least one active contact",
                "Must be assigned to an account manager",
                "Cannot be deleted if has active projects",
              ],
              "description": "Enterprise client organization details",
              "domain_specific": true,
              "lifecycle_states": [
                "prospect",
                "active",
                "inactive",
                "archived"
              ],
              "name": "Client",
              "relationships": [
                "Contact",
                "Project",
                "Campaign",
                "Invoice",
                "Account",
              ],
              "source": "enhanced_extraction",
            },
            {
              "attributes": [
                {
                  "description": "Unique identifier",
                  "name": "id",
                  "required": true,
                  "type": "string",
                },
                {
                  "description": "Prospect company name",
                  "name": "company_name",
                  "required": true,
                  "type": "string",
                },
                {
                  "description": "Potential deal value",
                  "name": "estimated_value",
                  "required": true,
                  "type": "decimal",
                },
                {
                  "description": "Lead source channel",
                  "name": "source",
                  "required": true,
                  "type": "string",
                },
                {
                  "description": "Lead owner employee ID",
                  "name": "owner_id",
                  "required": true,
                  "type": "string",
                },
                {
                  "description": "Lead qualification score",
                  "name": "score",
                  "required": false,
                  "type": "integer",
                },
                {
                  "description": "Last interaction date",
                  "name": "last_contact_date",
                  "required": false,
                  "type": "datetime",
                },
              ],
              "auto_generated": true,
              "business_rules": [
                "Must be assigned to a sales representative",
                "Score must be updated after each interaction",
                "Automatic notifications for stale leads",
              ],
              "description": "Potential client opportunity tracking",
              "domain_specific": true,
              "lifecycle_states": [
                "new",
                "qualified",
                "negotiating",
                "won",
                "lost",
              ],
              "name": "Lead",
              "relationships": ["Employee", "Contact", "Campaign"],
              "source": "enhanced_extraction",
            },
            {
              "attributes": [
                {
                  "description": "Unique identifier",
                  "name": "id",
                  "required": true,
                  "type": "string",
                },
                {
                  "description": "Project name",
                  "name": "name",
                  "required": true,
                  "type": "string",
                },
                {
                  "description": "Associated client",
                  "name": "client_id",
                  "required": true,
                  "type": "string",
                },
                {
                  "description": "Assigned PM",
                  "name": "project_manager_id",
                  "required": true,
                  "type": "string",
                },
                {
                  "description": "Total project budget",
                  "name": "budget",
                  "required": true,
                  "type": "decimal",
                },
                {
                  "description": "Project start date",
                  "name": "start_date",
                  "required": true,
                  "type": "datetime",
                },
                {
                  "description": "Expected completion date",
                  "name": "end_date",
                  "required": true,
                  "type": "datetime",
                },
                {
                  "description": "Project priority level",
                  "name": "priority",
                  "required": true,
                  "type": "string",
                },
              ],
              "auto_generated": true,
              "business_rules": [
                "Must have assigned project manager",
                "Cannot exceed budget without approval",
                "Must have defined deliverables",
              ],
              "description": "Client project management details",
              "domain_specific": true,
              "lifecycle_states": [
                "planning",
                "active",
                "on-hold",
                "completed",
                "cancelled",
              ],
              "name": "Project",
              "relationships": [
                "Client",
                "Employee",
                "Task",
                "Timeline",
                "Resource",
                "Deliverable",
              ],
              "source": "enhanced_extraction",
            },
            {
              "attributes": [
                {
                  "description": "Unique identifier",
                  "name": "id",
                  "required": true,
                  "type": "string",
                },
                {
                  "description": "Campaign name",
                  "name": "name",
                  "required": true,
                  "type": "string",
                },
                {
                  "description": "Associated client",
                  "name": "client_id",
                  "required": true,
                  "type": "string",
                },
                {
                  "description": "Campaign budget",
                  "name": "budget",
                  "required": true,
                  "type": "decimal",
                },
                {
                  "description": "Campaign start",
                  "name": "start_date",
                  "required": true,
                  "type": "datetime",
                },
                {
                  "description": "Campaign end",
                  "name": "end_date",
                  "required": true,
                  "type": "datetime",
                },
                {
                  "description": "Performance metrics",
                  "name": "kpis",
                  "required": true,
                  "type": "json",
                },
              ],
              "auto_generated": true,
              "business_rules": [
                "Must have defined KPIs",
                "Requires analytics tracking setup",
                "Budget tracking required",
              ],
              "description": "Marketing campaign tracking",
              "domain_specific": true,
              "lifecycle_states": [
                "draft",
                "active",
                "paused",
                "completed",
                "archived",
              ],
              "name": "Campaign",
              "relationships": ["Client", "Analytics", "Creative", "Task"],
              "source": "enhanced_extraction",
            },
          ],
          "data_flow_patterns": [
            {
              "flow_type": "transformation",
              "function": "CreateClientCampaignProposal",
              "input_entities": ["Client", "Campaign"],
              "output_entities": ["Project"],
            },
            {
              "flow_type": "transformation",
              "function": "TrackCampaignPerformance",
              "input_entities": ["Campaign", "Client"],
              "output_entities": ["Campaign"],
            },
            {
              "flow_type": "transformation",
              "function": "ManageCreativeWorkflow",
              "input_entities": ["Project", "Client"],
              "output_entities": ["Project"],
            },
            {
              "flow_type": "transformation",
              "function": "QualifyEnterpriseLeads",
              "input_entities": ["Lead"],
              "output_entities": ["Lead"],
            },
            {
              "flow_type": "transformation",
              "function": "GenerateClientReports",
              "input_entities": ["Client", "Campaign", "Project"],
              "output_entities": ["Client"],
            },
          ],
          "entity_relationships": [
            {
              "cardinality": "one_to_many",
              "from_entity": "Client",
              "relationship_type": "references",
              "to_entity": "Contact",
            },
            {
              "cardinality": "one_to_many",
              "from_entity": "Client",
              "relationship_type": "references",
              "to_entity": "Project",
            },
            {
              "cardinality": "one_to_many",
              "from_entity": "Client",
              "relationship_type": "references",
              "to_entity": "Campaign",
            },
            {
              "cardinality": "one_to_many",
              "from_entity": "Client",
              "relationship_type": "references",
              "to_entity": "Invoice",
            },
            {
              "cardinality": "one_to_many",
              "from_entity": "Client",
              "relationship_type": "references",
              "to_entity": "Account",
            },
            {
              "cardinality": "one_to_many",
              "from_entity": "Lead",
              "relationship_type": "references",
              "to_entity": "Employee",
            },
            {
              "cardinality": "one_to_many",
              "from_entity": "Lead",
              "relationship_type": "references",
              "to_entity": "Contact",
            },
            {
              "cardinality": "one_to_many",
              "from_entity": "Lead",
              "relationship_type": "references",
              "to_entity": "Campaign",
            },
            {
              "cardinality": "one_to_many",
              "from_entity": "Project",
              "relationship_type": "references",
              "to_entity": "Client",
            },
            {
              "cardinality": "one_to_many",
              "from_entity": "Project",
              "relationship_type": "references",
              "to_entity": "Employee",
            },
            {
              "cardinality": "one_to_many",
              "from_entity": "Project",
              "relationship_type": "references",
              "to_entity": "Task",
            },
            {
              "cardinality": "one_to_many",
              "from_entity": "Project",
              "relationship_type": "references",
              "to_entity": "Timeline",
            },
            {
              "cardinality": "one_to_many",
              "from_entity": "Project",
              "relationship_type": "references",
              "to_entity": "Resource",
            },
            {
              "cardinality": "one_to_many",
              "from_entity": "Project",
              "relationship_type": "references",
              "to_entity": "Deliverable",
            },
            {
              "cardinality": "one_to_many",
              "from_entity": "Campaign",
              "relationship_type": "references",
              "to_entity": "Client",
            },
            {
              "cardinality": "one_to_many",
              "from_entity": "Campaign",
              "relationship_type": "references",
              "to_entity": "Analytics",
            },
            {
              "cardinality": "one_to_many",
              "from_entity": "Campaign",
              "relationship_type": "references",
              "to_entity": "Creative",
            },
            {
              "cardinality": "one_to_many",
              "from_entity": "Campaign",
              "relationship_type": "references",
              "to_entity": "Task",
            },
          ],
          "reference_entities": [],
          "transaction_entities": [],
        },
        "section_4_functions_local_objectives": {
          "function_categories": {
            "analyze": ["GenerateClientReports"],
            "create": ["CreateClientCampaignProposal"],
            "delete": [],
            "process": [
              "TrackCampaignPerformance",
              "ManageCreativeWorkflow",
              "QualifyEnterpriseLeads",
            ],
            "read": [],
            "update": [],
          },
          "function_dependencies": [
            {
              "dependency_type": "data_input",
              "depends_on_entity": "Client",
              "function": "CreateClientCampaignProposal",
            },
            {
              "dependency_type": "data_input",
              "depends_on_entity": "Campaign",
              "function": "CreateClientCampaignProposal",
            },
            {
              "dependency_type": "data_input",
              "depends_on_entity": "Campaign",
              "function": "TrackCampaignPerformance",
            },
            {
              "dependency_type": "data_input",
              "depends_on_entity": "Client",
              "function": "TrackCampaignPerformance",
            },
            {
              "dependency_type": "data_input",
              "depends_on_entity": "Project",
              "function": "ManageCreativeWorkflow",
            },
            {
              "dependency_type": "data_input",
              "depends_on_entity": "Client",
              "function": "ManageCreativeWorkflow",
            },
            {
              "dependency_type": "data_input",
              "depends_on_entity": "Lead",
              "function": "QualifyEnterpriseLeads",
            },
            {
              "dependency_type": "data_input",
              "depends_on_entity": "Client",
              "function": "GenerateClientReports",
            },
            {
              "dependency_type": "data_input",
              "depends_on_entity": "Campaign",
              "function": "GenerateClientReports",
            },
            {
              "dependency_type": "data_input",
              "depends_on_entity": "Project",
              "function": "GenerateClientReports",
            },
          ],
          "lo_slots": [
            {
              "assigned_roles": [
                "Digital Account Manager",
                "Creative Lead",
                "Project Manager",
              ],
              "auto_generated": true,
              "business_rules": [
                "Proposals must be reviewed by Creative Lead before client presentation",
                "Resource availability must be verified before timeline commitment",
                "Budget must align with agency rate card",
              ],
              "complexity_level": "high",
              "description":
                  "Create and manage campaign proposals for enterprise clients with detailed scope, timeline, and creative requirements",
              "domain_specific": true,
              "inputs": [
                {
                  "attributes": [
                    "client_id",
                    "industry",
                    "budget_range",
                    "target_market",
                  ],
                  "entity": "Client",
                },
                {
                  "attributes": [
                    "campaign_type",
                    "objectives",
                    "deliverables",
                    "timeline",
                  ],
                  "entity": "Campaign",
                },
              ],
              "name": "CreateClientCampaignProposal",
              "outputs": [
                {
                  "attributes": [
                    "proposal_id",
                    "cost_estimate",
                    "resource_allocation",
                    "timeline_breakdown",
                  ],
                  "entity": "Project",
                },
              ],
              "source": "llm_generation",
              "validation_criteria": [
                "All required client information must be complete",
                "Timeline must account for review cycles",
                "Resource allocation must not exceed team capacity",
              ],
            },
            {
              "assigned_roles": [
                "Analytics Manager",
                "Marketing Campaign Specialist",
              ],
              "auto_generated": true,
              "business_rules": [
                "Data must be updated daily",
                "Alerts required for KPI deviations",
                "Client-specific reporting templates must be used",
              ],
              "complexity_level": "medium",
              "description":
                  "Monitor and analyze digital campaign metrics across multiple channels for enterprise clients",
              "domain_specific": true,
              "inputs": [
                {
                  "attributes": [
                    "campaign_id",
                    "channels",
                    "metrics",
                    "start_date",
                    "end_date",
                  ],
                  "entity": "Campaign",
                },
                {
                  "attributes": ["client_id", "kpi_targets"],
                  "entity": "Client",
                },
              ],
              "name": "TrackCampaignPerformance",
              "outputs": [
                {
                  "attributes": [
                    "performance_metrics",
                    "roi_analysis",
                    "optimization_recommendations",
                  ],
                  "entity": "Campaign",
                },
              ],
              "source": "llm_generation",
              "validation_criteria": [
                "All tracking pixels must be verified",
                "Data anomalies must be flagged",
                "Report access limited to authorized personnel",
              ],
            },
            {
              "assigned_roles": ["Creative Lead", "Project Manager"],
              "auto_generated": true,
              "business_rules": [
                "All assets must follow brand guidelines",
                "Maximum of three revision cycles",
                "Final approval required from client",
              ],
              "complexity_level": "medium",
              "description":
                  "Coordinate and track creative deliverables from concept to client approval",
              "domain_specific": true,
              "inputs": [
                {
                  "attributes": [
                    "project_id",
                    "creative_brief",
                    "asset_requirements",
                  ],
                  "entity": "Project",
                },
                {
                  "attributes": ["brand_guidelines", "approval_process"],
                  "entity": "Client",
                },
              ],
              "name": "ManageCreativeWorkflow",
              "outputs": [
                {
                  "attributes": [
                    "asset_status",
                    "revision_history",
                    "final_deliverables",
                  ],
                  "entity": "Project",
                },
              ],
              "source": "llm_generation",
              "validation_criteria": [
                "Asset specifications must match requirements",
                "All feedback must be documented",
                "Version control must be maintained",
              ],
            },
            {
              "assigned_roles": [
                "Digital Account Manager",
                "Operations Manager"
              ],
              "auto_generated": true,
              "business_rules": [
                "Minimum budget threshold must be met",
                "Project scope must match agency capabilities",
                "Geographic location must be within service area",
              ],
              "complexity_level": "low",
              "description":
                  "Evaluate and score incoming enterprise leads based on defined criteria",
              "domain_specific": true,
              "inputs": [
                {
                  "attributes": [
                    "company_info",
                    "budget_range",
                    "project_scope",
                    "timeline",
                  ],
                  "entity": "Lead",
                },
              ],
              "name": "QualifyEnterpriseLeads",
              "outputs": [
                {
                  "attributes": [
                    "qualification_score",
                    "opportunity_value",
                    "recommended_actions",
                  ],
                  "entity": "Lead",
                },
              ],
              "source": "llm_generation",
              "validation_criteria": [
                "Company information must be verified",
                "Decision maker must be identified",
                "Budget must be confirmed",
              ],
            },
            {
              "assigned_roles": [
                "Analytics Manager",
                "Digital Account Manager",
                "Marketing Campaign Specialist",
              ],
              "auto_generated": true,
              "business_rules": [
                "Reports must be generated by 5th of each month",
                "Custom metrics must be included per client requirements",
                "Executive summary required for all reports",
              ],
              "complexity_level": "high",
              "description":
                  "Create comprehensive performance reports for enterprise clients combining campaign metrics, project status, and recommendations",
              "domain_specific": true,
              "inputs": [
                {
                  "attributes": ["client_id", "reporting_preferences"],
                  "entity": "Client",
                },
                {
                  "attributes": ["performance_data", "budget_utilization"],
                  "entity": "Campaign",
                },
                {
                  "attributes": ["project_status", "deliverables_status"],
                  "entity": "Project",
                },
              ],
              "name": "GenerateClientReports",
              "outputs": [
                {
                  "attributes": [
                    "monthly_report",
                    "performance_summary",
                    "strategic_recommendations",
                  ],
                  "entity": "Client",
                },
              ],
              "source": "llm_generation",
              "validation_criteria": [
                "All data sources must be current",
                "Client-specific KPIs must be addressed",
                "Report format must match approved template",
              ],
            },
          ],
          "role_function_mapping": {
            "CreateClientCampaignProposal": [
              "Digital Account Manager",
              "Creative Lead",
              "Project Manager",
            ],
            "GenerateClientReports": [
              "Analytics Manager",
              "Digital Account Manager",
              "Marketing Campaign Specialist",
            ],
            "ManageCreativeWorkflow": ["Creative Lead", "Project Manager"],
            "QualifyEnterpriseLeads": [
              "Digital Account Manager",
              "Operations Manager",
            ],
            "TrackCampaignPerformance": [
              "Analytics Manager",
              "Marketing Campaign Specialist",
            ],
          },
          "validation_rules": [
            {
              "function": "CreateClientCampaignProposal",
              "rule": "All required client information must be complete",
              "type": "validation",
            },
            {
              "function": "CreateClientCampaignProposal",
              "rule": "Timeline must account for review cycles",
              "type": "validation",
            },
            {
              "function": "CreateClientCampaignProposal",
              "rule": "Resource allocation must not exceed team capacity",
              "type": "validation",
            },
            {
              "function": "CreateClientCampaignProposal",
              "rule":
                  "Proposals must be reviewed by Creative Lead before client presentation",
              "type": "validation",
            },
            {
              "function": "CreateClientCampaignProposal",
              "rule":
                  "Resource availability must be verified before timeline commitment",
              "type": "validation",
            },
            {
              "function": "CreateClientCampaignProposal",
              "rule": "Budget must align with agency rate card",
              "type": "validation",
            },
            {
              "function": "TrackCampaignPerformance",
              "rule": "All tracking pixels must be verified",
              "type": "validation",
            },
            {
              "function": "TrackCampaignPerformance",
              "rule": "Data anomalies must be flagged",
              "type": "validation",
            },
            {
              "function": "TrackCampaignPerformance",
              "rule": "Report access limited to authorized personnel",
              "type": "validation",
            },
            {
              "function": "TrackCampaignPerformance",
              "rule": "Data must be updated daily",
              "type": "validation",
            },
            {
              "function": "TrackCampaignPerformance",
              "rule": "Alerts required for KPI deviations",
              "type": "validation",
            },
            {
              "function": "TrackCampaignPerformance",
              "rule": "Client-specific reporting templates must be used",
              "type": "validation",
            },
            {
              "function": "ManageCreativeWorkflow",
              "rule": "Asset specifications must match requirements",
              "type": "validation",
            },
            {
              "function": "ManageCreativeWorkflow",
              "rule": "All feedback must be documented",
              "type": "validation",
            },
            {
              "function": "ManageCreativeWorkflow",
              "rule": "Version control must be maintained",
              "type": "validation",
            },
            {
              "function": "ManageCreativeWorkflow",
              "rule": "All assets must follow brand guidelines",
              "type": "validation",
            },
            {
              "function": "ManageCreativeWorkflow",
              "rule": "Maximum of three revision cycles",
              "type": "validation",
            },
            {
              "function": "ManageCreativeWorkflow",
              "rule": "Final approval required from client",
              "type": "validation",
            },
            {
              "function": "QualifyEnterpriseLeads",
              "rule": "Company information must be verified",
              "type": "validation",
            },
            {
              "function": "QualifyEnterpriseLeads",
              "rule": "Decision maker must be identified",
              "type": "validation",
            },
            {
              "function": "QualifyEnterpriseLeads",
              "rule": "Budget must be confirmed",
              "type": "validation",
            },
            {
              "function": "QualifyEnterpriseLeads",
              "rule": "Minimum budget threshold must be met",
              "type": "validation",
            },
            {
              "function": "QualifyEnterpriseLeads",
              "rule": "Project scope must match agency capabilities",
              "type": "validation",
            },
            {
              "function": "QualifyEnterpriseLeads",
              "rule": "Geographic location must be within service area",
              "type": "validation",
            },
            {
              "function": "GenerateClientReports",
              "rule": "All data sources must be current",
              "type": "validation",
            },
            {
              "function": "GenerateClientReports",
              "rule": "Client-specific KPIs must be addressed",
              "type": "validation",
            },
            {
              "function": "GenerateClientReports",
              "rule": "Report format must match approved template",
              "type": "validation",
            },
            {
              "function": "GenerateClientReports",
              "rule": "Reports must be generated by 5th of each month",
              "type": "validation",
            },
            {
              "function": "GenerateClientReports",
              "rule": "Custom metrics must be included per client requirements",
              "type": "validation",
            },
            {
              "function": "GenerateClientReports",
              "rule": "Executive summary required for all reports",
              "type": "validation",
            },
          ],
        },
        "section_5_workflows_global_objectives": {
          "go_slots": [
            {
              "auto_generated": true,
              "complexity_level": "high",
              "dependencies": [
                "CRM data accuracy",
                "Available account manager capacity",
              ],
              "description":
                  "Complete workflow from lead qualification to proposal creation and client onboarding",
              "domain_specific": true,
              "name": "Enterprise Lead-to-Client Conversion",
              "pathways": [
                {
                  "functions_used": [
                    "QualifyEnterpriseLeads",
                    "CreateClientCampaignProposal",
                  ],
                  "name": "Standard Conversion Path",
                  "steps": [
                    "Initial lead screening",
                    "Client requirements gathering",
                    "Proposal development",
                    "Campaign strategy creation",
                    "Contract signing",
                    "Client onboarding",
                  ],
                },
                {
                  "functions_used": [
                    "QualifyEnterpriseLeads",
                    "CreateClientCampaignProposal",
                  ],
                  "name": "Fast-track Path",
                  "steps": [
                    "Expedited qualification",
                    "Quick proposal generation",
                    "Rapid onboarding",
                  ],
                },
              ],
              "roles_involved": [
                "Account Manager",
                "Marketing Specialist",
                "Operations Manager",
              ],
              "source": "llm_generation",
              "success_criteria": [
                "Proposal accepted",
                "Client onboarded within 30 days",
                "Initial campaign strategy approved",
              ],
              "trigger_conditions": [
                "New enterprise lead received",
                "Lead meets minimum budget threshold",
              ],
            },
            {
              "auto_generated": true,
              "complexity_level": "medium",
              "dependencies": [
                "Creative assets completion",
                "Client approval processes",
              ],
              "description":
                  "End-to-end campaign management process including creative development, implementation, and performance tracking",
              "domain_specific": true,
              "name": "Campaign Execution and Monitoring",
              "pathways": [
                {
                  "functions_used": [
                    "ManageCreativeWorkflow",
                    "TrackCampaignPerformance",
                    "GenerateClientReports",
                  ],
                  "name": "Full Campaign Cycle",
                  "steps": [
                    "Creative brief creation",
                    "Asset development",
                    "Campaign setup",
                    "Launch execution",
                    "Performance monitoring",
                    "Client reporting",
                  ],
                },
                {
                  "functions_used": [
                    "TrackCampaignPerformance",
                    "GenerateClientReports",
                  ],
                  "name": "Optimization Path",
                  "steps": [
                    "Performance review",
                    "Strategy adjustment",
                    "Implementation of changes",
                  ],
                },
              ],
              "roles_involved": [
                "Project Manager",
                "Creative Designer",
                "Developer",
                "Data Analyst",
              ],
              "source": "llm_generation",
              "success_criteria": [
                "Campaign KPIs achieved",
                "Client satisfaction metrics met",
                "Timely reporting delivered",
              ],
              "trigger_conditions": [
                "Campaign brief approved",
                "Resources allocated",
              ],
            },
            {
              "auto_generated": true,
              "complexity_level": "medium",
              "dependencies": [
                "Data availability",
                "Analytics tools functionality",
              ],
              "description":
                  "Comprehensive analysis and reporting workflow for client campaigns and agency performance",
              "domain_specific": true,
              "name": "Performance Analytics and Reporting",
              "pathways": [
                {
                  "functions_used": [
                    "TrackCampaignPerformance",
                    "GenerateClientReports",
                  ],
                  "name": "Standard Reporting Path",
                  "steps": [
                    "Data collection",
                    "Performance analysis",
                    "Report generation",
                    "Client presentation preparation",
                    "Delivery and review",
                  ],
                },
                {
                  "functions_used": [
                    "TrackCampaignPerformance",
                    "GenerateClientReports",
                  ],
                  "name": "Deep Dive Analysis",
                  "steps": [
                    "Advanced data analysis",
                    "Trend identification",
                    "Strategy recommendations",
                    "Executive summary creation",
                  ],
                },
              ],
              "roles_involved": [
                "Data Analyst",
                "Account Manager",
                "Marketing Specialist",
              ],
              "source": "llm_generation",
              "success_criteria": [
                "Reports delivered on schedule",
                "Actionable insights provided",
                "Client feedback incorporated",
              ],
              "trigger_conditions": [
                "Monthly reporting cycle",
                "Client request for reports",
                "Campaign milestone reached",
              ],
            },
          ],
          "process_flows": [
            {
              "flow_type": "sequential",
              "functions_used": [
                "QualifyEnterpriseLeads",
                "CreateClientCampaignProposal",
              ],
              "pathway": "Standard Conversion Path",
              "steps": [
                "Initial lead screening",
                "Client requirements gathering",
                "Proposal development",
                "Campaign strategy creation",
                "Contract signing",
                "Client onboarding",
              ],
              "workflow": "Enterprise Lead-to-Client Conversion",
            },
            {
              "flow_type": "sequential",
              "functions_used": [
                "QualifyEnterpriseLeads",
                "CreateClientCampaignProposal",
              ],
              "pathway": "Fast-track Path",
              "steps": [
                "Expedited qualification",
                "Quick proposal generation",
                "Rapid onboarding",
              ],
              "workflow": "Enterprise Lead-to-Client Conversion",
            },
            {
              "flow_type": "sequential",
              "functions_used": [
                "ManageCreativeWorkflow",
                "TrackCampaignPerformance",
                "GenerateClientReports",
              ],
              "pathway": "Full Campaign Cycle",
              "steps": [
                "Creative brief creation",
                "Asset development",
                "Campaign setup",
                "Launch execution",
                "Performance monitoring",
                "Client reporting",
              ],
              "workflow": "Campaign Execution and Monitoring",
            },
            {
              "flow_type": "sequential",
              "functions_used": [
                "TrackCampaignPerformance",
                "GenerateClientReports",
              ],
              "pathway": "Optimization Path",
              "steps": [
                "Performance review",
                "Strategy adjustment",
                "Implementation of changes",
              ],
              "workflow": "Campaign Execution and Monitoring",
            },
            {
              "flow_type": "sequential",
              "functions_used": [
                "TrackCampaignPerformance",
                "GenerateClientReports",
              ],
              "pathway": "Standard Reporting Path",
              "steps": [
                "Data collection",
                "Performance analysis",
                "Report generation",
                "Client presentation preparation",
                "Delivery and review",
              ],
              "workflow": "Performance Analytics and Reporting",
            },
            {
              "flow_type": "sequential",
              "functions_used": [
                "TrackCampaignPerformance",
                "GenerateClientReports",
              ],
              "pathway": "Deep Dive Analysis",
              "steps": [
                "Advanced data analysis",
                "Trend identification",
                "Strategy recommendations",
                "Executive summary creation",
              ],
              "workflow": "Performance Analytics and Reporting",
            },
          ],
          "success_criteria": [
            {
              "criterion": "Proposal accepted",
              "measurement_type": "qualitative",
              "workflow": "Enterprise Lead-to-Client Conversion",
            },
            {
              "criterion": "Client onboarded within 30 days",
              "measurement_type": "qualitative",
              "workflow": "Enterprise Lead-to-Client Conversion",
            },
            {
              "criterion": "Initial campaign strategy approved",
              "measurement_type": "qualitative",
              "workflow": "Enterprise Lead-to-Client Conversion",
            },
            {
              "criterion": "Campaign KPIs achieved",
              "measurement_type": "qualitative",
              "workflow": "Campaign Execution and Monitoring",
            },
            {
              "criterion": "Client satisfaction metrics met",
              "measurement_type": "qualitative",
              "workflow": "Campaign Execution and Monitoring",
            },
            {
              "criterion": "Timely reporting delivered",
              "measurement_type": "qualitative",
              "workflow": "Campaign Execution and Monitoring",
            },
            {
              "criterion": "Reports delivered on schedule",
              "measurement_type": "qualitative",
              "workflow": "Performance Analytics and Reporting",
            },
            {
              "criterion": "Actionable insights provided",
              "measurement_type": "qualitative",
              "workflow": "Performance Analytics and Reporting",
            },
            {
              "criterion": "Client feedback incorporated",
              "measurement_type": "qualitative",
              "workflow": "Performance Analytics and Reporting",
            },
          ],
          "workflow_categories": {
            "administrative": [],
            "core_business": [
              "Enterprise Lead-to-Client Conversion",
              "Campaign Execution and Monitoring",
            ],
            "integration": [],
            "reporting": ["Performance Analytics and Reporting"],
          },
          "workflow_triggers": [
            {
              "trigger_condition": "New enterprise lead received",
              "trigger_type": "event",
              "workflow": "Enterprise Lead-to-Client Conversion",
            },
            {
              "trigger_condition": "Lead meets minimum budget threshold",
              "trigger_type": "event",
              "workflow": "Enterprise Lead-to-Client Conversion",
            },
            {
              "trigger_condition": "Campaign brief approved",
              "trigger_type": "event",
              "workflow": "Campaign Execution and Monitoring",
            },
            {
              "trigger_condition": "Resources allocated",
              "trigger_type": "event",
              "workflow": "Campaign Execution and Monitoring",
            },
            {
              "trigger_condition": "Monthly reporting cycle",
              "trigger_type": "event",
              "workflow": "Performance Analytics and Reporting",
            },
            {
              "trigger_condition": "Client request for reports",
              "trigger_type": "event",
              "workflow": "Performance Analytics and Reporting",
            },
            {
              "trigger_condition": "Campaign milestone reached",
              "trigger_type": "event",
              "workflow": "Performance Analytics and Reporting",
            },
          ],
        },
        "section_6_role_permissions": {
          "access_control_rules": [
            {
              "access_level": "write",
              "entity_access": ["Client", "Lead", "Project", "Campaign"],
              "function_access": [
                "CreateClientCampaignProposal",
                "TrackCampaignPerformance",
                "ManageCreativeWorkflow",
                "QualifyEnterpriseLeads",
                "GenerateClientReports",
              ],
              "role": "Digital Account Manager",
            },
            {
              "access_level": "write",
              "entity_access": ["Client", "Lead", "Project", "Campaign"],
              "function_access": [
                "CreateClientCampaignProposal",
                "TrackCampaignPerformance",
                "ManageCreativeWorkflow",
                "QualifyEnterpriseLeads",
                "GenerateClientReports",
              ],
              "role": "Creative Lead",
            },
            {
              "access_level": "write",
              "entity_access": ["Client", "Lead", "Project", "Campaign"],
              "function_access": [
                "CreateClientCampaignProposal",
                "TrackCampaignPerformance",
                "ManageCreativeWorkflow",
                "QualifyEnterpriseLeads",
                "GenerateClientReports",
              ],
              "role": "Project Manager",
            },
            {
              "access_level": "write",
              "entity_access": ["Client", "Lead", "Project", "Campaign"],
              "function_access": [
                "CreateClientCampaignProposal",
                "TrackCampaignPerformance",
                "ManageCreativeWorkflow",
                "QualifyEnterpriseLeads",
                "GenerateClientReports",
              ],
              "role": "Analytics Manager",
            },
            {
              "access_level": "write",
              "entity_access": ["Client", "Lead", "Project", "Campaign"],
              "function_access": [
                "CreateClientCampaignProposal",
                "TrackCampaignPerformance",
                "ManageCreativeWorkflow",
                "QualifyEnterpriseLeads",
                "GenerateClientReports",
              ],
              "role": "Operations Manager",
            },
            {
              "access_level": "write",
              "entity_access": ["Client", "Lead", "Project", "Campaign"],
              "function_access": [
                "CreateClientCampaignProposal",
                "TrackCampaignPerformance",
                "ManageCreativeWorkflow",
                "QualifyEnterpriseLeads",
                "GenerateClientReports",
              ],
              "role": "Marketing Campaign Specialist",
            },
          ],
          "permission_matrix": {
            "entities": {
              "Campaign": [
                "Digital Account Manager",
                "Creative Lead",
                "Project Manager",
                "Analytics Manager",
                "Operations Manager",
                "Marketing Campaign Specialist",
                "Chief Technology Officer (Cto)",
              ],
              "Client": [
                "Digital Account Manager",
                "Creative Lead",
                "Project Manager",
                "Analytics Manager",
                "Operations Manager",
                "Marketing Campaign Specialist",
                "Chief Technology Officer (Cto)",
              ],
              "Lead": [
                "Digital Account Manager",
                "Creative Lead",
                "Project Manager",
                "Analytics Manager",
                "Operations Manager",
                "Marketing Campaign Specialist",
                "Chief Technology Officer (Cto)",
              ],
              "Project": [
                "Digital Account Manager",
                "Creative Lead",
                "Project Manager",
                "Analytics Manager",
                "Operations Manager",
                "Marketing Campaign Specialist",
                "Chief Technology Officer (Cto)",
              ],
            },
            "functions": {
              "CreateClientCampaignProposal": [
                "Digital Account Manager",
                "Creative Lead",
                "Project Manager",
                "Analytics Manager",
                "Operations Manager",
                "Marketing Campaign Specialist",
              ],
              "GenerateClientReports": [
                "Digital Account Manager",
                "Creative Lead",
                "Project Manager",
                "Analytics Manager",
                "Operations Manager",
                "Marketing Campaign Specialist",
              ],
              "ManageCreativeWorkflow": [
                "Digital Account Manager",
                "Creative Lead",
                "Project Manager",
                "Analytics Manager",
                "Operations Manager",
                "Marketing Campaign Specialist",
              ],
              "QualifyEnterpriseLeads": [
                "Digital Account Manager",
                "Creative Lead",
                "Project Manager",
                "Analytics Manager",
                "Operations Manager",
                "Marketing Campaign Specialist",
              ],
              "TrackCampaignPerformance": [
                "Digital Account Manager",
                "Creative Lead",
                "Project Manager",
                "Analytics Manager",
                "Operations Manager",
                "Marketing Campaign Specialist",
              ],
            },
            "workflows": {
              "Campaign Execution and Monitoring": [
                "Digital Account Manager",
                "Creative Lead",
                "Project Manager",
                "Analytics Manager",
                "Operations Manager",
                "Marketing Campaign Specialist",
              ],
              "Enterprise Lead-to-Client Conversion": [
                "Digital Account Manager",
                "Creative Lead",
                "Project Manager",
                "Analytics Manager",
                "Operations Manager",
                "Marketing Campaign Specialist",
              ],
              "Performance Analytics and Reporting": [
                "Digital Account Manager",
                "Creative Lead",
                "Project Manager",
                "Analytics Manager",
                "Operations Manager",
                "Marketing Campaign Specialist",
              ],
            },
          },
          "role_permissions": {
            "Analytics Manager": {
              "authority_level": "write",
              "entity_access": ["Client", "Lead", "Project", "Campaign"],
              "escalation_authority": false,
              "function_execution": [
                "CreateClientCampaignProposal",
                "TrackCampaignPerformance",
                "ManageCreativeWorkflow",
                "QualifyEnterpriseLeads",
                "GenerateClientReports",
              ],
              "workflow_initiation": [
                "Enterprise Lead-to-Client Conversion",
                "Campaign Execution and Monitoring",
                "Performance Analytics and Reporting",
              ],
            },
            "Chief Technology Officer (Cto)": {
              "authority_level": "write",
              "entity_access": ["User", "Record", "Transaction"],
              "escalation_authority": false,
              "function_execution": ["full_access", "admin_rights"],
              "workflow_initiation": ["New Client Onboarding"],
            },
            "Creative Lead": {
              "authority_level": "write",
              "entity_access": ["Client", "Lead", "Project", "Campaign"],
              "escalation_authority": false,
              "function_execution": [
                "CreateClientCampaignProposal",
                "TrackCampaignPerformance",
                "ManageCreativeWorkflow",
                "QualifyEnterpriseLeads",
                "GenerateClientReports",
              ],
              "workflow_initiation": [
                "Enterprise Lead-to-Client Conversion",
                "Campaign Execution and Monitoring",
                "Performance Analytics and Reporting",
              ],
            },
            "Digital Account Manager": {
              "authority_level": "write",
              "entity_access": ["Client", "Lead", "Project", "Campaign"],
              "escalation_authority": false,
              "function_execution": [
                "CreateClientCampaignProposal",
                "TrackCampaignPerformance",
                "ManageCreativeWorkflow",
                "QualifyEnterpriseLeads",
                "GenerateClientReports",
              ],
              "workflow_initiation": [
                "Enterprise Lead-to-Client Conversion",
                "Campaign Execution and Monitoring",
                "Performance Analytics and Reporting",
              ],
            },
            "Marketing Campaign Specialist": {
              "authority_level": "write",
              "entity_access": ["Client", "Lead", "Project", "Campaign"],
              "escalation_authority": false,
              "function_execution": [
                "CreateClientCampaignProposal",
                "TrackCampaignPerformance",
                "ManageCreativeWorkflow",
                "QualifyEnterpriseLeads",
                "GenerateClientReports",
              ],
              "workflow_initiation": [
                "Enterprise Lead-to-Client Conversion",
                "Campaign Execution and Monitoring",
                "Performance Analytics and Reporting",
              ],
            },
            "Operations Manager": {
              "authority_level": "write",
              "entity_access": ["Client", "Lead", "Project", "Campaign"],
              "escalation_authority": false,
              "function_execution": [
                "CreateClientCampaignProposal",
                "TrackCampaignPerformance",
                "ManageCreativeWorkflow",
                "QualifyEnterpriseLeads",
                "GenerateClientReports",
              ],
              "workflow_initiation": [
                "Enterprise Lead-to-Client Conversion",
                "Campaign Execution and Monitoring",
                "Performance Analytics and Reporting",
              ],
            },
            "Project Manager": {
              "authority_level": "write",
              "entity_access": ["Client", "Lead", "Project", "Campaign"],
              "escalation_authority": false,
              "function_execution": [
                "CreateClientCampaignProposal",
                "TrackCampaignPerformance",
                "ManageCreativeWorkflow",
                "QualifyEnterpriseLeads",
                "GenerateClientReports",
              ],
              "workflow_initiation": [
                "Enterprise Lead-to-Client Conversion",
                "Campaign Execution and Monitoring",
                "Performance Analytics and Reporting",
              ],
            },
          },
          "security_requirements": [],
        },
        "section_7_intelligence": {
          "analytics_requirements": [
            {
              "frequency": "daily",
              "metric": "Client Retention Rate",
              "type": "performance",
              "visualization": "dashboard",
            },
            {
              "frequency": "daily",
              "metric": "Project Profit Margin",
              "type": "performance",
              "visualization": "dashboard",
            },
            {
              "frequency": "daily",
              "metric": "Campaign ROI",
              "type": "performance",
              "visualization": "dashboard",
            },
            {
              "frequency": "daily",
              "metric": "Client Satisfaction Score (CSAT)",
              "type": "performance",
              "visualization": "dashboard",
            },
            {
              "frequency": "daily",
              "metric": "Lead Conversion Rate",
              "type": "performance",
              "visualization": "dashboard",
            },
            {
              "frequency": "daily",
              "metric": "Average Project Timeline",
              "type": "performance",
              "visualization": "dashboard",
            },
            {
              "frequency": "daily",
              "metric": "Resource Utilization Rate",
              "type": "performance",
              "visualization": "dashboard",
            },
            {
              "frequency": "weekly",
              "metric": "Project profitability",
              "type": "financial",
            },
            {
              "frequency": "monthly",
              "metric": "Client satisfaction",
              "type": "quality",
            },
            {
              "frequency": "daily",
              "metric": "Resource utilization",
              "type": "operational",
            },
          ],
          "compliance_tracking": [],
          "intelligence_metadata": {
            "confidence_score": 0.9,
            "domain_context": {
              "confidence": 0.9,
              "primary": "digital_agency",
              "solution_type": "crm",
            },
            "generated_at": "2025-07-08T09:10:44.673354",
            "generation_method": "phase_9_llm_based",
          },
          "last_change": {
            "change_type": "modification",
            "impact_analysis": {
              "affected_sections": ["foundation", "organization"],
              "change_type": "modification",
              "confidence_score": 0.8,
              "cross_section_updates": {},
              "impact_level": "medium",
              "propagation_required": true,
              "validation_recommendations": [
                "Review affected sections",
                "Validate cross-references",
              ],
            },
            "modification_applied": {
              "permissions": ["full_access", "admin_rights"],
              "reports_to": "Agency Owner",
              "role_name": "Chief Technology Officer (Cto)",
              "summary": "Add Chief Technology Officer (Cto) role",
              "type": "add_role",
            },
            "timestamp": "2025-07-08T09:11:53.502214",
            "user_input":
                "I want to add a Chief Technology Officer (CTO) role who will oversee all technical development and report directly to me as the Agency Director. This person should have full access to development systems, code repositories, and technical infrastructure. They should also manage the development team and make technology decisions.",
          },
          "performance_metrics": [
            "Client Retention Rate",
            "Project Profit Margin",
            "Campaign ROI",
            "Client Satisfaction Score (CSAT)",
            "Lead Conversion Rate",
            "Average Project Timeline",
            "Resource Utilization Rate",
          ],
          "reporting_needs": [
            {
              "content": [
                "Performance metrics",
                "Team status",
                "Key indicators"
              ],
              "frequency": "daily",
              "report_name": "Digital Account Manager Dashboard",
              "target_role": "Digital Account Manager",
            },
            {
              "content": [
                "Performance metrics",
                "Team status",
                "Key indicators"
              ],
              "frequency": "daily",
              "report_name": "Project Manager Dashboard",
              "target_role": "Project Manager",
            },
            {
              "content": [
                "Performance metrics",
                "Team status",
                "Key indicators"
              ],
              "frequency": "daily",
              "report_name": "Analytics Manager Dashboard",
              "target_role": "Analytics Manager",
            },
            {
              "content": [
                "Performance metrics",
                "Team status",
                "Key indicators"
              ],
              "frequency": "daily",
              "report_name": "Operations Manager Dashboard",
              "target_role": "Operations Manager",
            },
            {
              "content": [
                {
                  "attributes": [
                    "monthly_report",
                    "performance_summary",
                    "strategic_recommendations",
                  ],
                  "entity": "Client",
                },
              ],
              "frequency": "on-demand",
              "report_name": "GenerateClientReports",
              "target_role": "All Users",
            },
          ],
        },
      },
    };
  }

  Map<String, dynamic> _getSampleData2() {
    return {
      "complete_json": {
        "section_1_user_persona_and_solution_archetype": {
          "solution_context": {
            "business_goals": [],
            "compliance_requirements": [],
            "success_metrics": [
              "Client Retention Rate",
              "Project Profit Margin",
              "Campaign ROI",
              "Client Satisfaction Score (CSAT)",
              "Lead Conversion Rate",
              "Average Project Timeline",
              "Resource Utilization Rate",
            ],
          },
          "solution_metadata": {
            "complexity_level": 5,
            "industry": "digital_agency",
            "region": "north_america",
            "solution_archetype": "standard",
            "solution_type": "crm",
            "team_size": "medium",
          },
          "user_personas": [
            {
              "description":
                  "Manages client relationships and oversees digital marketing campaigns for enterprise clients",
              "goals": ["Efficiently perform Digital Account Manager duties"],
              "needs": [
                "client relationship management",
                "campaign oversight",
                "revenue growth",
                "client strategy planning",
              ],
              "pain_points": ["Manual processes", "Lack of integration"],
              "role": "Digital Account Manager",
              "technical_proficiency": "medium",
            },
            {
              "description":
                  "Oversees design team and ensures creative deliverables meet client expectations",
              "goals": ["Efficiently perform Creative Lead duties"],
              "needs": [
                "creative direction",
                "design review",
                "brand compliance",
                "team management",
              ],
              "pain_points": ["Manual processes", "Lack of integration"],
              "role": "Creative Lead",
              "technical_proficiency": "medium",
            },
            {
              "description":
                  "Coordinates digital marketing projects and ensures timely delivery",
              "goals": ["Efficiently perform Project Manager duties"],
              "needs": [
                "project planning",
                "resource allocation",
                "timeline management",
                "stakeholder communication",
              ],
              "pain_points": ["Manual processes", "Lack of integration"],
              "role": "Project Manager",
              "technical_proficiency": "medium",
            },
          ],
        },
        "section_2_organization_information": {
          "organizational_context": {
            "industry": "digital_agency",
            "region": "north_america",
            "scale": "medium",
          },
          "roles": [
            {
              "authority_level": "senior",
              "auto_generated": true,
              "department": "Client Services",
              "description":
                  "Manages client relationships and oversees digital marketing campaigns for enterprise clients",
              "domain_specific": true,
              "key_functions": [
                "client relationship management",
                "campaign oversight",
                "revenue growth",
                "client strategy planning",
              ],
              "name": "Digital Account Manager",
              "permissions": [
                "client data access",
                "campaign management",
                "budget approval",
                "client reporting",
              ],
              "reports_to": "Operations Manager",
              "source": "llm_generation",
            },
            {
              "authority_level": "manager",
              "auto_generated": true,
              "department": "Creative",
              "description":
                  "Oversees design team and ensures creative deliverables meet client expectations",
              "domain_specific": true,
              "key_functions": [
                "creative direction",
                "design review",
                "brand compliance",
                "team management",
              ],
              "name": "Creative Lead",
              "permissions": [
                "asset library access",
                "creative approval",
                "resource allocation",
                "template management",
              ],
              "reports_to": "Operations Manager",
              "source": "llm_generation",
            },
            {
              "authority_level": "senior",
              "auto_generated": true,
              "department": "Project Management",
              "description":
                  "Coordinates digital marketing projects and ensures timely delivery",
              "domain_specific": true,
              "key_functions": [
                "project planning",
                "resource allocation",
                "timeline management",
                "stakeholder communication",
              ],
              "name": "Project Manager",
              "permissions": [
                "project dashboard access",
                "task assignment",
                "timeline management",
                "resource scheduling",
              ],
              "reports_to": "Operations Manager",
              "source": "llm_generation",
            },
            {
              "authority_level": "manager",
              "auto_generated": true,
              "department": "Analytics",
              "description":
                  "Leads data analysis and performance reporting for digital campaigns",
              "domain_specific": true,
              "key_functions": [
                "performance analysis",
                "reporting automation",
                "insights generation",
                "data strategy",
              ],
              "name": "Analytics Manager",
              "permissions": [
                "analytics tools access",
                "data export",
                "report creation",
                "dashboard configuration",
              ],
              "reports_to": "Operations Manager",
              "source": "llm_generation",
            },
            {
              "authority_level": "executive",
              "auto_generated": true,
              "department": "Operations",
              "description":
                  "Oversees agency operations and ensures efficient workflow across departments",
              "domain_specific": true,
              "key_functions": [
                "strategic planning",
                "resource optimization",
                "process improvement",
                "department coordination",
              ],
              "name": "Operations Manager",
              "permissions": [
                "system admin",
                "user management",
                "workflow configuration",
                "full system access",
              ],
              "reports_to": null,
              "source": "llm_generation",
            },
            {
              "authority_level": "senior",
              "auto_generated": true,
              "department": "Marketing",
              "description":
                  "Executes and optimizes digital marketing campaigns",
              "domain_specific": true,
              "key_functions": [
                "campaign execution",
                "performance optimization",
                "content coordination",
                "campaign analytics",
              ],
              "name": "Marketing Campaign Specialist",
              "permissions": [
                "campaign tools access",
                "content management",
                "performance tracking",
                "reporting tools",
              ],
              "reports_to": "Marketing Manager",
              "source": "llm_generation",
            },
          ],
          "stakeholders": {
            "administrators": [],
            "decision_makers": [
              "Creative Lead",
              "Analytics Manager",
              "Operations Manager",
            ],
            "primary_users": [
              "Digital Account Manager",
              "Project Manager",
              "Marketing Campaign Specialist",
            ],
          },
          "team_structure": {
            "departments": [
              "Creative",
              "Marketing",
              "Analytics",
              "Operations",
              "Client Services",
              "Project Management",
            ],
            "reporting_structure": {
              "Analytics Manager": "Operations Manager",
              "Creative Lead": "Operations Manager",
              "Digital Account Manager": "Operations Manager",
              "Marketing Campaign Specialist": "Marketing Manager",
              "Project Manager": "Operations Manager",
            },
            "total_size": 15,
          },
        },
        "section_3_entities_and_attributes": {
          "core_business_entities": [
            {
              "attributes": [
                {
                  "description": "Unique identifier",
                  "name": "id",
                  "required": true,
                  "type": "string",
                },
                {
                  "description": "Official company name",
                  "name": "company_name",
                  "required": true,
                  "type": "string",
                },
                {
                  "description": "Client's industry sector",
                  "name": "industry",
                  "required": true,
                  "type": "string",
                },
                {
                  "description": "Client's annual revenue",
                  "name": "annual_revenue",
                  "required": false,
                  "type": "decimal",
                },
                {
                  "description": "Assigned account manager",
                  "name": "account_manager_id",
                  "required": true,
                  "type": "string",
                },
                {
                  "description": "Client's primary location",
                  "name": "location",
                  "required": true,
                  "type": "string",
                },
                {
                  "description": "Client onboarding date",
                  "name": "onboarding_date",
                  "required": true,
                  "type": "datetime",
                },
              ],
              "auto_generated": true,
              "business_rules": [
                "Must have at least one active contact",
                "Must be assigned to an account manager",
                "Cannot be deleted if has active projects",
              ],
              "description": "Enterprise client organization details",
              "domain_specific": true,
              "lifecycle_states": [
                "prospect",
                "active",
                "inactive",
                "archived"
              ],
              "name": "Client",
              "relationships": [
                "Contact",
                "Project",
                "Campaign",
                "Invoice",
                "Account",
              ],
              "source": "enhanced_extraction",
            },
            {
              "attributes": [
                {
                  "description": "Unique identifier",
                  "name": "id",
                  "required": true,
                  "type": "string",
                },
                {
                  "description": "Prospect company name",
                  "name": "company_name",
                  "required": true,
                  "type": "string",
                },
                {
                  "description": "Potential deal value",
                  "name": "estimated_value",
                  "required": true,
                  "type": "decimal",
                },
                {
                  "description": "Lead source channel",
                  "name": "source",
                  "required": true,
                  "type": "string",
                },
                {
                  "description": "Lead owner employee ID",
                  "name": "owner_id",
                  "required": true,
                  "type": "string",
                },
                {
                  "description": "Lead qualification score",
                  "name": "score",
                  "required": false,
                  "type": "integer",
                },
                {
                  "description": "Last interaction date",
                  "name": "last_contact_date",
                  "required": false,
                  "type": "datetime",
                },
              ],
              "auto_generated": true,
              "business_rules": [
                "Must be assigned to a sales representative",
                "Score must be updated after each interaction",
                "Automatic notifications for stale leads",
              ],
              "description": "Potential client opportunity tracking",
              "domain_specific": true,
              "lifecycle_states": [
                "new",
                "qualified",
                "negotiating",
                "won",
                "lost",
              ],
              "name": "Lead",
              "relationships": ["Employee", "Contact", "Campaign"],
              "source": "enhanced_extraction",
            },
            {
              "attributes": [
                {
                  "description": "Unique identifier",
                  "name": "id",
                  "required": true,
                  "type": "string",
                },
                {
                  "description": "Project name",
                  "name": "name",
                  "required": true,
                  "type": "string",
                },
                {
                  "description": "Associated client",
                  "name": "client_id",
                  "required": true,
                  "type": "string",
                },
                {
                  "description": "Assigned PM",
                  "name": "project_manager_id",
                  "required": true,
                  "type": "string",
                },
                {
                  "description": "Total project budget",
                  "name": "budget",
                  "required": true,
                  "type": "decimal",
                },
                {
                  "description": "Project start date",
                  "name": "start_date",
                  "required": true,
                  "type": "datetime",
                },
                {
                  "description": "Expected completion date",
                  "name": "end_date",
                  "required": true,
                  "type": "datetime",
                },
                {
                  "description": "Project priority level",
                  "name": "priority",
                  "required": true,
                  "type": "string",
                },
              ],
              "auto_generated": true,
              "business_rules": [
                "Must have assigned project manager",
                "Cannot exceed budget without approval",
                "Must have defined deliverables",
              ],
              "description": "Client project management details",
              "domain_specific": true,
              "lifecycle_states": [
                "planning",
                "active",
                "on-hold",
                "completed",
                "cancelled",
              ],
              "name": "Project",
              "relationships": [
                "Client",
                "Employee",
                "Task",
                "Timeline",
                "Resource",
                "Deliverable",
              ],
              "source": "enhanced_extraction",
            },
            {
              "attributes": [
                {
                  "description": "Unique identifier",
                  "name": "id",
                  "required": true,
                  "type": "string",
                },
                {
                  "description": "Campaign name",
                  "name": "name",
                  "required": true,
                  "type": "string",
                },
                {
                  "description": "Associated client",
                  "name": "client_id",
                  "required": true,
                  "type": "string",
                },
                {
                  "description": "Campaign budget",
                  "name": "budget",
                  "required": true,
                  "type": "decimal",
                },
                {
                  "description": "Campaign start",
                  "name": "start_date",
                  "required": true,
                  "type": "datetime",
                },
                {
                  "description": "Campaign end",
                  "name": "end_date",
                  "required": true,
                  "type": "datetime",
                },
                {
                  "description": "Performance metrics",
                  "name": "kpis",
                  "required": true,
                  "type": "json",
                },
              ],
              "auto_generated": true,
              "business_rules": [
                "Must have defined KPIs",
                "Requires analytics tracking setup",
                "Budget tracking required",
              ],
              "description": "Marketing campaign tracking",
              "domain_specific": true,
              "lifecycle_states": [
                "draft",
                "active",
                "paused",
                "completed",
                "archived",
              ],
              "name": "Campaign",
              "relationships": ["Client", "Analytics", "Creative", "Task"],
              "source": "enhanced_extraction",
            },
          ],
          "data_flow_patterns": [
            {
              "flow_type": "transformation",
              "function": "CreateClientCampaignProposal",
              "input_entities": ["Client", "Campaign"],
              "output_entities": ["Project"],
            },
            {
              "flow_type": "transformation",
              "function": "TrackCampaignPerformance",
              "input_entities": ["Campaign", "Client"],
              "output_entities": ["Campaign"],
            },
            {
              "flow_type": "transformation",
              "function": "ManageCreativeWorkflow",
              "input_entities": ["Project", "Client"],
              "output_entities": ["Project"],
            },
            {
              "flow_type": "transformation",
              "function": "QualifyEnterpriseLeads",
              "input_entities": ["Lead"],
              "output_entities": ["Lead"],
            },
            {
              "flow_type": "transformation",
              "function": "GenerateClientReports",
              "input_entities": ["Client", "Campaign", "Project"],
              "output_entities": ["Client"],
            },
          ],
          "entity_relationships": [
            {
              "cardinality": "one_to_many",
              "from_entity": "Client",
              "relationship_type": "references",
              "to_entity": "Contact",
            },
            {
              "cardinality": "one_to_many",
              "from_entity": "Client",
              "relationship_type": "references",
              "to_entity": "Project",
            },
            {
              "cardinality": "one_to_many",
              "from_entity": "Client",
              "relationship_type": "references",
              "to_entity": "Campaign",
            },
            {
              "cardinality": "one_to_many",
              "from_entity": "Client",
              "relationship_type": "references",
              "to_entity": "Invoice",
            },
            {
              "cardinality": "one_to_many",
              "from_entity": "Client",
              "relationship_type": "references",
              "to_entity": "Account",
            },
            {
              "cardinality": "one_to_many",
              "from_entity": "Lead",
              "relationship_type": "references",
              "to_entity": "Employee",
            },
            {
              "cardinality": "one_to_many",
              "from_entity": "Lead",
              "relationship_type": "references",
              "to_entity": "Contact",
            },
            {
              "cardinality": "one_to_many",
              "from_entity": "Lead",
              "relationship_type": "references",
              "to_entity": "Campaign",
            },
            {
              "cardinality": "one_to_many",
              "from_entity": "Project",
              "relationship_type": "references",
              "to_entity": "Client",
            },
            {
              "cardinality": "one_to_many",
              "from_entity": "Project",
              "relationship_type": "references",
              "to_entity": "Employee",
            },
            {
              "cardinality": "one_to_many",
              "from_entity": "Project",
              "relationship_type": "references",
              "to_entity": "Task",
            },
            {
              "cardinality": "one_to_many",
              "from_entity": "Project",
              "relationship_type": "references",
              "to_entity": "Timeline",
            },
            {
              "cardinality": "one_to_many",
              "from_entity": "Project",
              "relationship_type": "references",
              "to_entity": "Resource",
            },
            {
              "cardinality": "one_to_many",
              "from_entity": "Project",
              "relationship_type": "references",
              "to_entity": "Deliverable",
            },
            {
              "cardinality": "one_to_many",
              "from_entity": "Campaign",
              "relationship_type": "references",
              "to_entity": "Client",
            },
            {
              "cardinality": "one_to_many",
              "from_entity": "Campaign",
              "relationship_type": "references",
              "to_entity": "Analytics",
            },
            {
              "cardinality": "one_to_many",
              "from_entity": "Campaign",
              "relationship_type": "references",
              "to_entity": "Creative",
            },
            {
              "cardinality": "one_to_many",
              "from_entity": "Campaign",
              "relationship_type": "references",
              "to_entity": "Task",
            },
          ],
          "reference_entities": [],
          "transaction_entities": [],
        },
        "section_4_functions_local_objectives": {
          "function_categories": {
            "analyze": ["GenerateClientReports"],
            "create": ["CreateClientCampaignProposal"],
            "delete": [],
            "process": [
              "TrackCampaignPerformance",
              "ManageCreativeWorkflow",
              "QualifyEnterpriseLeads",
            ],
            "read": [],
            "update": [],
          },
          "function_dependencies": [
            {
              "dependency_type": "data_input",
              "depends_on_entity": "Client",
              "function": "CreateClientCampaignProposal",
            },
            {
              "dependency_type": "data_input",
              "depends_on_entity": "Campaign",
              "function": "CreateClientCampaignProposal",
            },
            {
              "dependency_type": "data_input",
              "depends_on_entity": "Campaign",
              "function": "TrackCampaignPerformance",
            },
            {
              "dependency_type": "data_input",
              "depends_on_entity": "Client",
              "function": "TrackCampaignPerformance",
            },
            {
              "dependency_type": "data_input",
              "depends_on_entity": "Project",
              "function": "ManageCreativeWorkflow",
            },
            {
              "dependency_type": "data_input",
              "depends_on_entity": "Client",
              "function": "ManageCreativeWorkflow",
            },
            {
              "dependency_type": "data_input",
              "depends_on_entity": "Lead",
              "function": "QualifyEnterpriseLeads",
            },
            {
              "dependency_type": "data_input",
              "depends_on_entity": "Client",
              "function": "GenerateClientReports",
            },
            {
              "dependency_type": "data_input",
              "depends_on_entity": "Campaign",
              "function": "GenerateClientReports",
            },
            {
              "dependency_type": "data_input",
              "depends_on_entity": "Project",
              "function": "GenerateClientReports",
            },
          ],
          "lo_slots": [
            {
              "assigned_roles": [
                "Digital Account Manager",
                "Creative Lead",
                "Project Manager",
              ],
              "auto_generated": true,
              "business_rules": [
                "Proposals must be reviewed by Creative Lead before client presentation",
                "Resource availability must be verified before timeline commitment",
                "Budget must align with agency rate card",
              ],
              "complexity_level": "high",
              "description":
                  "Create and manage campaign proposals for enterprise clients with detailed scope, timeline, and creative requirements",
              "domain_specific": true,
              "inputs": [
                {
                  "attributes": [
                    "client_id",
                    "industry",
                    "budget_range",
                    "target_market",
                  ],
                  "entity": "Client",
                },
                {
                  "attributes": [
                    "campaign_type",
                    "objectives",
                    "deliverables",
                    "timeline",
                  ],
                  "entity": "Campaign",
                },
              ],
              "name": "CreateClientCampaignProposal",
              "outputs": [
                {
                  "attributes": [
                    "proposal_id",
                    "cost_estimate",
                    "resource_allocation",
                    "timeline_breakdown",
                  ],
                  "entity": "Project",
                },
              ],
              "source": "llm_generation",
              "validation_criteria": [
                "All required client information must be complete",
                "Timeline must account for review cycles",
                "Resource allocation must not exceed team capacity",
              ],
            },
            {
              "assigned_roles": [
                "Analytics Manager",
                "Marketing Campaign Specialist",
              ],
              "auto_generated": true,
              "business_rules": [
                "Data must be updated daily",
                "Alerts required for KPI deviations",
                "Client-specific reporting templates must be used",
              ],
              "complexity_level": "medium",
              "description":
                  "Monitor and analyze digital campaign metrics across multiple channels for enterprise clients",
              "domain_specific": true,
              "inputs": [
                {
                  "attributes": [
                    "campaign_id",
                    "channels",
                    "metrics",
                    "start_date",
                    "end_date",
                  ],
                  "entity": "Campaign",
                },
                {
                  "attributes": ["client_id", "kpi_targets"],
                  "entity": "Client",
                },
              ],
              "name": "TrackCampaignPerformance",
              "outputs": [
                {
                  "attributes": [
                    "performance_metrics",
                    "roi_analysis",
                    "optimization_recommendations",
                  ],
                  "entity": "Campaign",
                },
              ],
              "source": "llm_generation",
              "validation_criteria": [
                "All tracking pixels must be verified",
                "Data anomalies must be flagged",
                "Report access limited to authorized personnel",
              ],
            },
            {
              "assigned_roles": ["Creative Lead", "Project Manager"],
              "auto_generated": true,
              "business_rules": [
                "All assets must follow brand guidelines",
                "Maximum of three revision cycles",
                "Final approval required from client",
              ],
              "complexity_level": "medium",
              "description":
                  "Coordinate and track creative deliverables from concept to client approval",
              "domain_specific": true,
              "inputs": [
                {
                  "attributes": [
                    "project_id",
                    "creative_brief",
                    "asset_requirements",
                  ],
                  "entity": "Project",
                },
                {
                  "attributes": ["brand_guidelines", "approval_process"],
                  "entity": "Client",
                },
              ],
              "name": "ManageCreativeWorkflow",
              "outputs": [
                {
                  "attributes": [
                    "asset_status",
                    "revision_history",
                    "final_deliverables",
                  ],
                  "entity": "Project",
                },
              ],
              "source": "llm_generation",
              "validation_criteria": [
                "Asset specifications must match requirements",
                "All feedback must be documented",
                "Version control must be maintained",
              ],
            },
            {
              "assigned_roles": [
                "Digital Account Manager",
                "Operations Manager"
              ],
              "auto_generated": true,
              "business_rules": [
                "Minimum budget threshold must be met",
                "Project scope must match agency capabilities",
                "Geographic location must be within service area",
              ],
              "complexity_level": "low",
              "description":
                  "Evaluate and score incoming enterprise leads based on defined criteria",
              "domain_specific": true,
              "inputs": [
                {
                  "attributes": [
                    "company_info",
                    "budget_range",
                    "project_scope",
                    "timeline",
                  ],
                  "entity": "Lead",
                },
              ],
              "name": "QualifyEnterpriseLeads",
              "outputs": [
                {
                  "attributes": [
                    "qualification_score",
                    "opportunity_value",
                    "recommended_actions",
                  ],
                  "entity": "Lead",
                },
              ],
              "source": "llm_generation",
              "validation_criteria": [
                "Company information must be verified",
                "Decision maker must be identified",
                "Budget must be confirmed",
              ],
            },
            {
              "assigned_roles": [
                "Analytics Manager",
                "Digital Account Manager",
                "Marketing Campaign Specialist",
              ],
              "auto_generated": true,
              "business_rules": [
                "Reports must be generated by 5th of each month",
                "Custom metrics must be included per client requirements",
                "Executive summary required for all reports",
              ],
              "complexity_level": "high",
              "description":
                  "Create comprehensive performance reports for enterprise clients combining campaign metrics, project status, and recommendations",
              "domain_specific": true,
              "inputs": [
                {
                  "attributes": ["client_id", "reporting_preferences"],
                  "entity": "Client",
                },
                {
                  "attributes": ["performance_data", "budget_utilization"],
                  "entity": "Campaign",
                },
                {
                  "attributes": ["project_status", "deliverables_status"],
                  "entity": "Project",
                },
              ],
              "name": "GenerateClientReports",
              "outputs": [
                {
                  "attributes": [
                    "monthly_report",
                    "performance_summary",
                    "strategic_recommendations",
                  ],
                  "entity": "Client",
                },
              ],
              "source": "llm_generation",
              "validation_criteria": [
                "All data sources must be current",
                "Client-specific KPIs must be addressed",
                "Report format must match approved template",
              ],
            },
          ],
          "role_function_mapping": {
            "CreateClientCampaignProposal": [
              "Digital Account Manager",
              "Creative Lead",
              "Project Manager",
            ],
            "GenerateClientReports": [
              "Analytics Manager",
              "Digital Account Manager",
              "Marketing Campaign Specialist",
            ],
            "ManageCreativeWorkflow": ["Creative Lead", "Project Manager"],
            "QualifyEnterpriseLeads": [
              "Digital Account Manager",
              "Operations Manager",
            ],
            "TrackCampaignPerformance": [
              "Analytics Manager",
              "Marketing Campaign Specialist",
            ],
          },
          "validation_rules": [
            {
              "function": "CreateClientCampaignProposal",
              "rule": "All required client information must be complete",
              "type": "validation",
            },
            {
              "function": "CreateClientCampaignProposal",
              "rule": "Timeline must account for review cycles",
              "type": "validation",
            },
            {
              "function": "CreateClientCampaignProposal",
              "rule": "Resource allocation must not exceed team capacity",
              "type": "validation",
            },
            {
              "function": "CreateClientCampaignProposal",
              "rule":
                  "Proposals must be reviewed by Creative Lead before client presentation",
              "type": "validation",
            },
            {
              "function": "CreateClientCampaignProposal",
              "rule":
                  "Resource availability must be verified before timeline commitment",
              "type": "validation",
            },
            {
              "function": "CreateClientCampaignProposal",
              "rule": "Budget must align with agency rate card",
              "type": "validation",
            },
            {
              "function": "TrackCampaignPerformance",
              "rule": "All tracking pixels must be verified",
              "type": "validation",
            },
            {
              "function": "TrackCampaignPerformance",
              "rule": "Data anomalies must be flagged",
              "type": "validation",
            },
            {
              "function": "TrackCampaignPerformance",
              "rule": "Report access limited to authorized personnel",
              "type": "validation",
            },
            {
              "function": "TrackCampaignPerformance",
              "rule": "Data must be updated daily",
              "type": "validation",
            },
            {
              "function": "TrackCampaignPerformance",
              "rule": "Alerts required for KPI deviations",
              "type": "validation",
            },
            {
              "function": "TrackCampaignPerformance",
              "rule": "Client-specific reporting templates must be used",
              "type": "validation",
            },
            {
              "function": "ManageCreativeWorkflow",
              "rule": "Asset specifications must match requirements",
              "type": "validation",
            },
            {
              "function": "ManageCreativeWorkflow",
              "rule": "All feedback must be documented",
              "type": "validation",
            },
            {
              "function": "ManageCreativeWorkflow",
              "rule": "Version control must be maintained",
              "type": "validation",
            },
            {
              "function": "ManageCreativeWorkflow",
              "rule": "All assets must follow brand guidelines",
              "type": "validation",
            },
            {
              "function": "ManageCreativeWorkflow",
              "rule": "Maximum of three revision cycles",
              "type": "validation",
            },
            {
              "function": "ManageCreativeWorkflow",
              "rule": "Final approval required from client",
              "type": "validation",
            },
            {
              "function": "QualifyEnterpriseLeads",
              "rule": "Company information must be verified",
              "type": "validation",
            },
            {
              "function": "QualifyEnterpriseLeads",
              "rule": "Decision maker must be identified",
              "type": "validation",
            },
            {
              "function": "QualifyEnterpriseLeads",
              "rule": "Budget must be confirmed",
              "type": "validation",
            },
            {
              "function": "QualifyEnterpriseLeads",
              "rule": "Minimum budget threshold must be met",
              "type": "validation",
            },
            {
              "function": "QualifyEnterpriseLeads",
              "rule": "Project scope must match agency capabilities",
              "type": "validation",
            },
            {
              "function": "QualifyEnterpriseLeads",
              "rule": "Geographic location must be within service area",
              "type": "validation",
            },
            {
              "function": "GenerateClientReports",
              "rule": "All data sources must be current",
              "type": "validation",
            },
            {
              "function": "GenerateClientReports",
              "rule": "Client-specific KPIs must be addressed",
              "type": "validation",
            },
            {
              "function": "GenerateClientReports",
              "rule": "Report format must match approved template",
              "type": "validation",
            },
            {
              "function": "GenerateClientReports",
              "rule": "Reports must be generated by 5th of each month",
              "type": "validation",
            },
            {
              "function": "GenerateClientReports",
              "rule": "Custom metrics must be included per client requirements",
              "type": "validation",
            },
            {
              "function": "GenerateClientReports",
              "rule": "Executive summary required for all reports",
              "type": "validation",
            },
          ],
        },
        "section_5_workflows_global_objectives": {
          "go_slots": [
            {
              "auto_generated": true,
              "complexity_level": "high",
              "dependencies": [
                "CRM data accuracy",
                "Available account manager capacity",
              ],
              "description":
                  "Complete workflow from lead qualification to proposal creation and client onboarding",
              "domain_specific": true,
              "name": "Enterprise Lead-to-Client Conversion",
              "pathways": [
                {
                  "functions_used": [
                    "QualifyEnterpriseLeads",
                    "CreateClientCampaignProposal",
                  ],
                  "name": "Standard Conversion Path",
                  "steps": [
                    "Initial lead screening",
                    "Client requirements gathering",
                    "Proposal development",
                    "Campaign strategy creation",
                    "Contract signing",
                    "Client onboarding",
                  ],
                },
                {
                  "functions_used": [
                    "QualifyEnterpriseLeads",
                    "CreateClientCampaignProposal",
                  ],
                  "name": "Fast-track Path",
                  "steps": [
                    "Expedited qualification",
                    "Quick proposal generation",
                    "Rapid onboarding",
                  ],
                },
              ],
              "roles_involved": [
                "Account Manager",
                "Marketing Specialist",
                "Operations Manager",
              ],
              "source": "llm_generation",
              "success_criteria": [
                "Proposal accepted",
                "Client onboarded within 30 days",
                "Initial campaign strategy approved",
              ],
              "trigger_conditions": [
                "New enterprise lead received",
                "Lead meets minimum budget threshold",
              ],
            },
            {
              "auto_generated": true,
              "complexity_level": "medium",
              "dependencies": [
                "Creative assets completion",
                "Client approval processes",
              ],
              "description":
                  "End-to-end campaign management process including creative development, implementation, and performance tracking",
              "domain_specific": true,
              "name": "Campaign Execution and Monitoring",
              "pathways": [
                {
                  "functions_used": [
                    "ManageCreativeWorkflow",
                    "TrackCampaignPerformance",
                    "GenerateClientReports",
                  ],
                  "name": "Full Campaign Cycle",
                  "steps": [
                    "Creative brief creation",
                    "Asset development",
                    "Campaign setup",
                    "Launch execution",
                    "Performance monitoring",
                    "Client reporting",
                  ],
                },
                {
                  "functions_used": [
                    "TrackCampaignPerformance",
                    "GenerateClientReports",
                  ],
                  "name": "Optimization Path",
                  "steps": [
                    "Performance review",
                    "Strategy adjustment",
                    "Implementation of changes",
                  ],
                },
              ],
              "roles_involved": [
                "Project Manager",
                "Creative Designer",
                "Developer",
                "Data Analyst",
              ],
              "source": "llm_generation",
              "success_criteria": [
                "Campaign KPIs achieved",
                "Client satisfaction metrics met",
                "Timely reporting delivered",
              ],
              "trigger_conditions": [
                "Campaign brief approved",
                "Resources allocated",
              ],
            },
            {
              "auto_generated": true,
              "complexity_level": "medium",
              "dependencies": [
                "Data availability",
                "Analytics tools functionality",
              ],
              "description":
                  "Comprehensive analysis and reporting workflow for client campaigns and agency performance",
              "domain_specific": true,
              "name": "Performance Analytics and Reporting",
              "pathways": [
                {
                  "functions_used": [
                    "TrackCampaignPerformance",
                    "GenerateClientReports",
                  ],
                  "name": "Standard Reporting Path",
                  "steps": [
                    "Data collection",
                    "Performance analysis",
                    "Report generation",
                    "Client presentation preparation",
                    "Delivery and review",
                  ],
                },
                {
                  "functions_used": [
                    "TrackCampaignPerformance",
                    "GenerateClientReports",
                  ],
                  "name": "Deep Dive Analysis",
                  "steps": [
                    "Advanced data analysis",
                    "Trend identification",
                    "Strategy recommendations",
                    "Executive summary creation",
                  ],
                },
              ],
              "roles_involved": [
                "Data Analyst",
                "Account Manager",
                "Marketing Specialist",
              ],
              "source": "llm_generation",
              "success_criteria": [
                "Reports delivered on schedule",
                "Actionable insights provided",
                "Client feedback incorporated",
              ],
              "trigger_conditions": [
                "Monthly reporting cycle",
                "Client request for reports",
                "Campaign milestone reached",
              ],
            },
          ],
          "process_flows": [
            {
              "flow_type": "sequential",
              "functions_used": [
                "QualifyEnterpriseLeads",
                "CreateClientCampaignProposal",
              ],
              "pathway": "Standard Conversion Path",
              "steps": [
                "Initial lead screening",
                "Client requirements gathering",
                "Proposal development",
                "Campaign strategy creation",
                "Contract signing",
                "Client onboarding",
              ],
              "workflow": "Enterprise Lead-to-Client Conversion",
            },
            {
              "flow_type": "sequential",
              "functions_used": [
                "QualifyEnterpriseLeads",
                "CreateClientCampaignProposal",
              ],
              "pathway": "Fast-track Path",
              "steps": [
                "Expedited qualification",
                "Quick proposal generation",
                "Rapid onboarding",
              ],
              "workflow": "Enterprise Lead-to-Client Conversion",
            },
            {
              "flow_type": "sequential",
              "functions_used": [
                "ManageCreativeWorkflow",
                "TrackCampaignPerformance",
                "GenerateClientReports",
              ],
              "pathway": "Full Campaign Cycle",
              "steps": [
                "Creative brief creation",
                "Asset development",
                "Campaign setup",
                "Launch execution",
                "Performance monitoring",
                "Client reporting",
              ],
              "workflow": "Campaign Execution and Monitoring",
            },
            {
              "flow_type": "sequential",
              "functions_used": [
                "TrackCampaignPerformance",
                "GenerateClientReports",
              ],
              "pathway": "Optimization Path",
              "steps": [
                "Performance review",
                "Strategy adjustment",
                "Implementation of changes",
              ],
              "workflow": "Campaign Execution and Monitoring",
            },
            {
              "flow_type": "sequential",
              "functions_used": [
                "TrackCampaignPerformance",
                "GenerateClientReports",
              ],
              "pathway": "Standard Reporting Path",
              "steps": [
                "Data collection",
                "Performance analysis",
                "Report generation",
                "Client presentation preparation",
                "Delivery and review",
              ],
              "workflow": "Performance Analytics and Reporting",
            },
            {
              "flow_type": "sequential",
              "functions_used": [
                "TrackCampaignPerformance",
                "GenerateClientReports",
              ],
              "pathway": "Deep Dive Analysis",
              "steps": [
                "Advanced data analysis",
                "Trend identification",
                "Strategy recommendations",
                "Executive summary creation",
              ],
              "workflow": "Performance Analytics and Reporting",
            },
          ],
          "success_criteria": [
            {
              "criterion": "Proposal accepted",
              "measurement_type": "qualitative",
              "workflow": "Enterprise Lead-to-Client Conversion",
            },
            {
              "criterion": "Client onboarded within 30 days",
              "measurement_type": "qualitative",
              "workflow": "Enterprise Lead-to-Client Conversion",
            },
            {
              "criterion": "Initial campaign strategy approved",
              "measurement_type": "qualitative",
              "workflow": "Enterprise Lead-to-Client Conversion",
            },
            {
              "criterion": "Campaign KPIs achieved",
              "measurement_type": "qualitative",
              "workflow": "Campaign Execution and Monitoring",
            },
            {
              "criterion": "Client satisfaction metrics met",
              "measurement_type": "qualitative",
              "workflow": "Campaign Execution and Monitoring",
            },
            {
              "criterion": "Timely reporting delivered",
              "measurement_type": "qualitative",
              "workflow": "Campaign Execution and Monitoring",
            },
            {
              "criterion": "Reports delivered on schedule",
              "measurement_type": "qualitative",
              "workflow": "Performance Analytics and Reporting",
            },
            {
              "criterion": "Actionable insights provided",
              "measurement_type": "qualitative",
              "workflow": "Performance Analytics and Reporting",
            },
            {
              "criterion": "Client feedback incorporated",
              "measurement_type": "qualitative",
              "workflow": "Performance Analytics and Reporting",
            },
          ],
          "workflow_categories": {
            "administrative": [],
            "core_business": [
              "Enterprise Lead-to-Client Conversion",
              "Campaign Execution and Monitoring",
            ],
            "integration": [],
            "reporting": ["Performance Analytics and Reporting"],
          },
          "workflow_triggers": [
            {
              "trigger_condition": "New enterprise lead received",
              "trigger_type": "event",
              "workflow": "Enterprise Lead-to-Client Conversion",
            },
            {
              "trigger_condition": "Lead meets minimum budget threshold",
              "trigger_type": "event",
              "workflow": "Enterprise Lead-to-Client Conversion",
            },
            {
              "trigger_condition": "Campaign brief approved",
              "trigger_type": "event",
              "workflow": "Campaign Execution and Monitoring",
            },
            {
              "trigger_condition": "Resources allocated",
              "trigger_type": "event",
              "workflow": "Campaign Execution and Monitoring",
            },
            {
              "trigger_condition": "Monthly reporting cycle",
              "trigger_type": "event",
              "workflow": "Performance Analytics and Reporting",
            },
            {
              "trigger_condition": "Client request for reports",
              "trigger_type": "event",
              "workflow": "Performance Analytics and Reporting",
            },
            {
              "trigger_condition": "Campaign milestone reached",
              "trigger_type": "event",
              "workflow": "Performance Analytics and Reporting",
            },
          ],
        },
        "section_6_role_permissions": {
          "access_control_rules": [
            {
              "access_level": "write",
              "entity_access": ["Client", "Lead", "Project", "Campaign"],
              "function_access": [
                "CreateClientCampaignProposal",
                "TrackCampaignPerformance",
                "ManageCreativeWorkflow",
                "QualifyEnterpriseLeads",
                "GenerateClientReports",
              ],
              "role": "Digital Account Manager",
            },
            {
              "access_level": "write",
              "entity_access": ["Client", "Lead", "Project", "Campaign"],
              "function_access": [
                "CreateClientCampaignProposal",
                "TrackCampaignPerformance",
                "ManageCreativeWorkflow",
                "QualifyEnterpriseLeads",
                "GenerateClientReports",
              ],
              "role": "Creative Lead",
            },
            {
              "access_level": "write",
              "entity_access": ["Client", "Lead", "Project", "Campaign"],
              "function_access": [
                "CreateClientCampaignProposal",
                "TrackCampaignPerformance",
                "ManageCreativeWorkflow",
                "QualifyEnterpriseLeads",
                "GenerateClientReports",
              ],
              "role": "Project Manager",
            },
            {
              "access_level": "write",
              "entity_access": ["Client", "Lead", "Project", "Campaign"],
              "function_access": [
                "CreateClientCampaignProposal",
                "TrackCampaignPerformance",
                "ManageCreativeWorkflow",
                "QualifyEnterpriseLeads",
                "GenerateClientReports",
              ],
              "role": "Analytics Manager",
            },
            {
              "access_level": "write",
              "entity_access": ["Client", "Lead", "Project", "Campaign"],
              "function_access": [
                "CreateClientCampaignProposal",
                "TrackCampaignPerformance",
                "ManageCreativeWorkflow",
                "QualifyEnterpriseLeads",
                "GenerateClientReports",
              ],
              "role": "Operations Manager",
            },
            {
              "access_level": "write",
              "entity_access": ["Client", "Lead", "Project", "Campaign"],
              "function_access": [
                "CreateClientCampaignProposal",
                "TrackCampaignPerformance",
                "ManageCreativeWorkflow",
                "QualifyEnterpriseLeads",
                "GenerateClientReports",
              ],
              "role": "Marketing Campaign Specialist",
            },
          ],
          "permission_matrix": {
            "entities": {
              "Campaign": [
                "Digital Account Manager",
                "Creative Lead",
                "Project Manager",
                "Analytics Manager",
                "Operations Manager",
                "Marketing Campaign Specialist",
              ],
              "Client": [
                "Digital Account Manager",
                "Creative Lead",
                "Project Manager",
                "Analytics Manager",
                "Operations Manager",
                "Marketing Campaign Specialist",
              ],
              "Lead": [
                "Digital Account Manager",
                "Creative Lead",
                "Project Manager",
                "Analytics Manager",
                "Operations Manager",
                "Marketing Campaign Specialist",
              ],
              "Project": [
                "Digital Account Manager",
                "Creative Lead",
                "Project Manager",
                "Analytics Manager",
                "Operations Manager",
                "Marketing Campaign Specialist",
              ],
            },
            "functions": {
              "CreateClientCampaignProposal": [
                "Digital Account Manager",
                "Creative Lead",
                "Project Manager",
                "Analytics Manager",
                "Operations Manager",
                "Marketing Campaign Specialist",
              ],
              "GenerateClientReports": [
                "Digital Account Manager",
                "Creative Lead",
                "Project Manager",
                "Analytics Manager",
                "Operations Manager",
                "Marketing Campaign Specialist",
              ],
              "ManageCreativeWorkflow": [
                "Digital Account Manager",
                "Creative Lead",
                "Project Manager",
                "Analytics Manager",
                "Operations Manager",
                "Marketing Campaign Specialist",
              ],
              "QualifyEnterpriseLeads": [
                "Digital Account Manager",
                "Creative Lead",
                "Project Manager",
                "Analytics Manager",
                "Operations Manager",
                "Marketing Campaign Specialist",
              ],
              "TrackCampaignPerformance": [
                "Digital Account Manager",
                "Creative Lead",
                "Project Manager",
                "Analytics Manager",
                "Operations Manager",
                "Marketing Campaign Specialist",
              ],
            },
            "workflows": {
              "Campaign Execution and Monitoring": [
                "Digital Account Manager",
                "Creative Lead",
                "Project Manager",
                "Analytics Manager",
                "Operations Manager",
                "Marketing Campaign Specialist",
              ],
              "Enterprise Lead-to-Client Conversion": [
                "Digital Account Manager",
                "Creative Lead",
                "Project Manager",
                "Analytics Manager",
                "Operations Manager",
                "Marketing Campaign Specialist",
              ],
              "Performance Analytics and Reporting": [
                "Digital Account Manager",
                "Creative Lead",
                "Project Manager",
                "Analytics Manager",
                "Operations Manager",
                "Marketing Campaign Specialist",
              ],
            },
          },
          "role_permissions": {
            "Analytics Manager": {
              "authority_level": "write",
              "entity_access": ["Client", "Lead", "Project", "Campaign"],
              "escalation_authority": false,
              "function_execution": [
                "CreateClientCampaignProposal",
                "TrackCampaignPerformance",
                "ManageCreativeWorkflow",
                "QualifyEnterpriseLeads",
                "GenerateClientReports",
              ],
              "workflow_initiation": [
                "Enterprise Lead-to-Client Conversion",
                "Campaign Execution and Monitoring",
                "Performance Analytics and Reporting",
              ],
            },
            "Creative Lead": {
              "authority_level": "write",
              "entity_access": ["Client", "Lead", "Project", "Campaign"],
              "escalation_authority": false,
              "function_execution": [
                "CreateClientCampaignProposal",
                "TrackCampaignPerformance",
                "ManageCreativeWorkflow",
                "QualifyEnterpriseLeads",
                "GenerateClientReports",
              ],
              "workflow_initiation": [
                "Enterprise Lead-to-Client Conversion",
                "Campaign Execution and Monitoring",
                "Performance Analytics and Reporting",
              ],
            },
            "Digital Account Manager": {
              "authority_level": "write",
              "entity_access": ["Client", "Lead", "Project", "Campaign"],
              "escalation_authority": false,
              "function_execution": [
                "CreateClientCampaignProposal",
                "TrackCampaignPerformance",
                "ManageCreativeWorkflow",
                "QualifyEnterpriseLeads",
                "GenerateClientReports",
              ],
              "workflow_initiation": [
                "Enterprise Lead-to-Client Conversion",
                "Campaign Execution and Monitoring",
                "Performance Analytics and Reporting",
              ],
            },
            "Marketing Campaign Specialist": {
              "authority_level": "write",
              "entity_access": ["Client", "Lead", "Project", "Campaign"],
              "escalation_authority": false,
              "function_execution": [
                "CreateClientCampaignProposal",
                "TrackCampaignPerformance",
                "ManageCreativeWorkflow",
                "QualifyEnterpriseLeads",
                "GenerateClientReports",
              ],
              "workflow_initiation": [
                "Enterprise Lead-to-Client Conversion",
                "Campaign Execution and Monitoring",
                "Performance Analytics and Reporting",
              ],
            },
            "Operations Manager": {
              "authority_level": "write",
              "entity_access": ["Client", "Lead", "Project", "Campaign"],
              "escalation_authority": false,
              "function_execution": [
                "CreateClientCampaignProposal",
                "TrackCampaignPerformance",
                "ManageCreativeWorkflow",
                "QualifyEnterpriseLeads",
                "GenerateClientReports",
              ],
              "workflow_initiation": [
                "Enterprise Lead-to-Client Conversion",
                "Campaign Execution and Monitoring",
                "Performance Analytics and Reporting",
              ],
            },
            "Project Manager": {
              "authority_level": "write",
              "entity_access": ["Client", "Lead", "Project", "Campaign"],
              "escalation_authority": false,
              "function_execution": [
                "CreateClientCampaignProposal",
                "TrackCampaignPerformance",
                "ManageCreativeWorkflow",
                "QualifyEnterpriseLeads",
                "GenerateClientReports",
              ],
              "workflow_initiation": [
                "Enterprise Lead-to-Client Conversion",
                "Campaign Execution and Monitoring",
                "Performance Analytics and Reporting",
              ],
            },
          },
          "security_requirements": [],
        },
        "section_7_intelligence": {
          "analytics_requirements": [
            {
              "frequency": "daily",
              "metric": "Client Retention Rate",
              "type": "performance",
              "visualization": "dashboard",
            },
            {
              "frequency": "daily",
              "metric": "Project Profit Margin",
              "type": "performance",
              "visualization": "dashboard",
            },
            {
              "frequency": "daily",
              "metric": "Campaign ROI",
              "type": "performance",
              "visualization": "dashboard",
            },
            {
              "frequency": "daily",
              "metric": "Client Satisfaction Score (CSAT)",
              "type": "performance",
              "visualization": "dashboard",
            },
            {
              "frequency": "daily",
              "metric": "Lead Conversion Rate",
              "type": "performance",
              "visualization": "dashboard",
            },
            {
              "frequency": "daily",
              "metric": "Average Project Timeline",
              "type": "performance",
              "visualization": "dashboard",
            },
            {
              "frequency": "daily",
              "metric": "Resource Utilization Rate",
              "type": "performance",
              "visualization": "dashboard",
            },
            {
              "frequency": "weekly",
              "metric": "Project profitability",
              "type": "financial",
            },
            {
              "frequency": "monthly",
              "metric": "Client satisfaction",
              "type": "quality",
            },
            {
              "frequency": "daily",
              "metric": "Resource utilization",
              "type": "operational",
            },
          ],
          "compliance_tracking": [],
          "intelligence_metadata": {
            "confidence_score": 0.9,
            "domain_context": {
              "confidence": 0.9,
              "primary": "digital_agency",
              "solution_type": "crm",
            },
            "generated_at": "2025-07-08T09:10:44.673354",
            "generation_method": "phase_9_llm_based",
          },
          "performance_metrics": [
            "Client Retention Rate",
            "Project Profit Margin",
            "Campaign ROI",
            "Client Satisfaction Score (CSAT)",
            "Lead Conversion Rate",
            "Average Project Timeline",
            "Resource Utilization Rate",
          ],
          "reporting_needs": [
            {
              "content": [
                "Performance metrics",
                "Team status",
                "Key indicators"
              ],
              "frequency": "daily",
              "report_name": "Digital Account Manager Dashboard",
              "target_role": "Digital Account Manager",
            },
            {
              "content": [
                "Performance metrics",
                "Team status",
                "Key indicators"
              ],
              "frequency": "daily",
              "report_name": "Project Manager Dashboard",
              "target_role": "Project Manager",
            },
            {
              "content": [
                "Performance metrics",
                "Team status",
                "Key indicators"
              ],
              "frequency": "daily",
              "report_name": "Analytics Manager Dashboard",
              "target_role": "Analytics Manager",
            },
            {
              "content": [
                "Performance metrics",
                "Team status",
                "Key indicators"
              ],
              "frequency": "daily",
              "report_name": "Operations Manager Dashboard",
              "target_role": "Operations Manager",
            },
            {
              "content": [
                {
                  "attributes": [
                    "monthly_report",
                    "performance_summary",
                    "strategic_recommendations",
                  ],
                  "entity": "Client",
                },
              ],
              "frequency": "on-demand",
              "report_name": "GenerateClientReports",
              "target_role": "All Users",
            },
          ],
        },
      },
    };
  }

  Map<String, dynamic> _getLargeDataSet() {
    // Generate a large dataset for performance testing
    Map<String, dynamic> largeData = {
      "performance_test": {
        "metadata": {"test_type": "large_dataset", "generated_items": 1000},
      },
    };

    // Add many sections
    for (int i = 1; i <= 50; i++) {
      largeData["section_$i"] = {
        "title": "Section $i",
        "description": "This is section number $i with sample data",
        "items": List.generate(
          20,
          (index) => {
            "id": "item_${i}_$index",
            "name": "Item $index in Section $i",
            "value": index * i,
            "enabled": index % 2 == 0,
            "metadata": {
              "created_at": "2024-01-01T00:00:00Z",
              "updated_at": "2024-01-${(index % 28) + 1}T12:00:00Z",
              "tags": ["tag1", "tag2", "tag${index % 5}"],
            },
          },
        ),
      };
    }

    return largeData;
  }

  Map<String, dynamic> _getDeepNestingData() {
    return {
      "deep_nesting_test": {
        "level_1": {
          "level_2": {
            "level_3": {
              "level_4": {
                "level_5": {
                  "level_6": {
                    "level_7": {
                      "level_8": {
                        "level_9": {
                          "level_10": {
                            "final_value": "Deep nesting test completed",
                            "nested_array": [
                              {
                                "item_1": {
                                  "sub_item_1": "value1",
                                  "sub_item_2": "value2",
                                },
                              },
                              {
                                "item_2": {
                                  "sub_item_1": "value3",
                                  "sub_item_2": "value4",
                                },
                              },
                            ],
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
        "parallel_structure": {
          "branch_a": {
            "data": ["item1", "item2", "item3"],
          },
          "branch_b": {
            "data": ["item4", "item5", "item6"],
          },
        },
      },
    };
  }
}

class EnhancedCollapsibleViewer extends StatefulWidget {
  final Map<String, dynamic> jsonData;
  final bool showAppBar;
  final EdgeInsetsGeometry? padding;

  const EnhancedCollapsibleViewer({
    Key? key,
    required this.jsonData,
    this.showAppBar = true,
    this.padding,
  }) : super(key: key);

  @override
  State<EnhancedCollapsibleViewer> createState() =>
      _EnhancedCollapsibleViewerState();
}

class _EnhancedCollapsibleViewerState extends State<EnhancedCollapsibleViewer> {
  late ScrollController _scrollController;
  Map<String, dynamic> _data = {};
  Set<String> _expandedKeys = {};

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _processJsonData();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _processJsonData() {
    if (widget.jsonData.containsKey('complete_json') &&
        widget.jsonData['complete_json'] is Map) {
      _data = widget.jsonData['complete_json'];
    } else if (widget.jsonData.containsKey('data') &&
        widget.jsonData['data'] is Map) {
      _data = widget.jsonData['data'];
    } else {
      _data = widget.jsonData;
    }
  }

  String _formatKey(String key) {
    return key
        .replaceAll('_', ' ')
        .split(' ')
        .map((word) => word.isNotEmpty
            ? '${word[0].toUpperCase()}${word.substring(1).toLowerCase()}'
            : '')
        .join(' ');
  }

  @override
  Widget build(BuildContext context) {
    Widget content = SingleChildScrollView(
      controller: _scrollController,
      padding: widget.padding ??
          const EdgeInsets.symmetric(horizontal: 10.0, vertical: 10.0),
      child: ExpansionTileGroup(
        toggleType: ToggleType.expandOnlyCurrent,
        spaceBetweenItem: 10,
        children: _buildTopLevelItems(),
      ),
    );

    if (widget.showAppBar) {
      return Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          title: const Text('Document Viewer'),
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          elevation: 1,
          actions: [
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () {
                setState(() {
                  _processJsonData();
                });
              },
            ),
          ],
        ),
        body: content,
        floatingActionButton: FloatingActionButton.small(
          onPressed: () => _scrollController.animateTo(
            0,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          ),
          backgroundColor: Colors.blue,
          child: const Icon(Icons.keyboard_arrow_up, color: Colors.white),
        ),
      );
    }

    return Scaffold(
        body: Container(
      color: Colors.grey[50],
      child: content,
    ));
  }

  List<ExpansionTileCard> _buildTopLevelItems() {
    List<ExpansionTileCard> items = [];
    List<String> keys = _data.keys.toList();
    // keys.sort();

    for (int i = 0; i < keys.length; i++) {
      String key = keys[i];
      dynamic value = _data[key];
      if (value != null) {
        items.add(_buildTopLevelCard(key, value, i + 1));
      }
    }

    return items;
  }

  ExpansionTileCard _buildTopLevelCard(String key, dynamic value, int number) {
    bool hasChildren = _hasExpandableContent(value);
    bool isExpanded = _expandedKeys.contains(key);

    if (!hasChildren) {
      return ExpansionTileCard(
        isEnableExpanded: false,
        initiallyExpanded: isExpanded,
        key: Key(key),
        childrenPadding: EdgeInsets.zero,
        title: MouseRegion(
          cursor:
              hasChildren ? SystemMouseCursors.click : SystemMouseCursors.none,
          child: Text(
            // '$number. ${_formatKey(key)}',
            _formatKey(key),
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s12,
              fontWeight: isExpanded ? FontWeight.bold : FontWeight.normal,
              color: AppColors.black,
              fontFamily: FontManager.fontFamilyTiemposText,
            ),
          ),
        ),
        onExpansionChanged: (value) {
          if (value) {
            _expandedKeys.add(key);
          } else {
            // _expandedKeys.remove(key);
            _expandedKeys.clear();
          }
          setState(() {});
        },
        trailing: SizedBox.shrink(),
        collapsedBorderColor: Color(0xFFB4B4B4),
        expendedBorderColor: Color(0xFF0058FF),
        // isDefaultVerticalPadding: true,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(
            color: isExpanded ? Color(0xFF0058FF) : Color(0xFFB4B4B4),
            width: isExpanded ? 1 : 0.5,
          ),
        ),
        tilePadding: EdgeInsets.symmetric(horizontal: 8),
        children: [],
      );
    }

    return ExpansionTileCard(
      initiallyExpanded: isExpanded,
      key: Key(key),
      childrenPadding: EdgeInsets.zero,
      title: MouseRegion(
        cursor:
            hasChildren ? SystemMouseCursors.click : SystemMouseCursors.none,
        child: Text(
          // '$number. ${_formatKey(key)}',
          _formatKey(key),
          style: getTextStyle(isExpanded: isExpanded),
        ),
      ),
      onExpansionChanged: (value) {
        if (value) {
          _expandedKeys.add(key);
        } else {
          _expandedKeys.remove(key);
        }
        setState(() {});
      },
      trailing: SizedBox.shrink(),
      collapsedBorderColor: Color(0xFFB4B4B4),
      expendedBorderColor: Color(0xFF0058FF),
      // isDefaultVerticalPadding: true,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: isExpanded ? Color(0xFF0058FF) : Color(0xFFB4B4B4),
          width: isExpanded ? 1 : 0.5,
        ),
      ),
      tilePadding: EdgeInsets.symmetric(horizontal: 8),
      children: [
        Container(
          decoration: BoxDecoration(
              border: Border(
                  top: BorderSide(color: Color(0xFFB4B4B4), width: 0.8))),
          margin: const EdgeInsets.fromLTRB(10, 0, 10, 10),
          padding: const EdgeInsets.fromLTRB(0, 10, 0, 0),
          child: _buildContent(value, '$number', parentKey: key),
        ),
      ],
    );
  }

  Widget _buildContent(
    dynamic value,
    String parentNumber, {
    String? parentKey,
  }) {
    if (parentKey == 'solution_metadata' && value is Map<String, dynamic>) {
      return _buildMetadataGrid(value);
    }

    if (value is Map<String, dynamic>) {
      return _buildMapContent(value, parentNumber);
    } else if (value is List) {
      if (_shouldRenderAsTable(value)) {
        return _buildTableFromList(value);
      }
      return _buildListContent(value, parentNumber);
    } else {
      return _buildTextContent(value.toString());
    }
  }

  bool _shouldRenderAsTable(List<dynamic> list) {
    if (list.isEmpty) return false;
    if (list.every((item) => item is Map<String, dynamic>)) {
      // Consider lists with uniform keys as tabular data
      final firstKeys = (list.first as Map<String, dynamic>).keys.toSet();
      return list.every((item) =>
          (item as Map<String, dynamic>).keys.toSet().containsAll(firstKeys));
    }
    return false;
  }

  Widget _buildTableFromList(List<dynamic> list) {
    final headers = (list.first as Map<String, dynamic>).keys.toList();

    return Padding(
      padding: const EdgeInsets.only(top: 5.0),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: DataTable(
          columnSpacing: 10,
          dataRowMinHeight: 24,
          dataRowMaxHeight: 80,
          headingRowHeight: 32,

          decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(
                  color: Color(
                    0xffA3A3A3,
                  ),
                  width: 0.5)),
          // headingRowColor: MaterialStateProperty.all(Colors.grey[200]),
          columns: headers
              .map((key) => DataColumn(
                    label: SizedBox(
                      width: 120,
                      child: Text(
                        _formatKey(key),
                        style: getTextStyle(isExpanded: true),
                      ),
                    ),
                  ))
              .toList(),
          rows: list.map((item) {
            final rowMap = item as Map<String, dynamic>;
            return DataRow(
              cells: headers.map((key) {
                final value = rowMap[key];
                String displayString =
                    value is List ? value.join(', ') : value?.toString() ?? '';
                return DataCell(
                  Container(
                    padding: EdgeInsets.all(4),
                    alignment: Alignment.topLeft,
                    width: 120,
                    child: displayString.length > 50
                        ? Tooltip(
                            margin: EdgeInsets.all(8),
                            message: displayString,
                            child: Text(
                              displayString,
                              overflow: TextOverflow.ellipsis,
                              maxLines: 3,
                              style: getTextStyle(),
                            ),
                          )
                        : Text(
                            displayString,
                            overflow: TextOverflow.ellipsis,
                            maxLines: 3,
                            style: getTextStyle(),
                          ),
                  ),
                );
              }).toList(),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildMapContent(Map<String, dynamic> map, String parentNumber) {
    List<String> keys = map.keys.toList();
    // keys.sort();

    return ExpansionTileGroup(
      toggleType: ToggleType.expandOnlyCurrent,
      children: keys.asMap().entries.map((entry) {
        int index = entry.key;
        String key = entry.value;
        dynamic value = map[key];
        String currentNumber = '$parentNumber.${index + 1}';

        return _buildSubExpansionTile(key, value, currentNumber);
      }).toList(),
    );
  }

  ExpansionTileCard _buildSubExpansionTile(
      String key, dynamic value, String number) {
    bool hasChildren = _hasExpandableContent(value);
    String keyString = '$number-$key';
    bool isExpanded = _expandedKeys.contains(keyString);

    if (!hasChildren) {
      return ExpansionTileCard(
        key: Key(keyString),
        childrenPadding: EdgeInsets.zero,
        elevation: 0,
        tilePadding: EdgeInsets.only(left: 10, top: 5, bottom: 5),
        isDefaultVerticalPadding: false,
        title: MouseRegion(
          cursor: SystemMouseCursors.basic,
          child: RichText(
            text: TextSpan(
              children: [
                TextSpan(
                    // text: '$number ${_formatKey(key)}: ',
                    text: '${_formatKey(key)}: ',
                    style: getTextStyle(isExpanded: true)),
                TextSpan(
                  text: value.toString(),
                  style: getTextStyle(),
                ),
              ],
            ),
          ),
        ),
        isEnableExpanded: false,
        trailingIcon: SizedBox.shrink(),
        children: [],
      );
    }

    return ExpansionTileCard(
      initiallyExpanded: isExpanded,
      key: Key(keyString),
      childrenPadding: EdgeInsets.zero,
      elevation: 0,
      trailing: SizedBox.shrink(),
      isDefaultVerticalPadding: false,
      title: MouseRegion(
        cursor:
            hasChildren ? SystemMouseCursors.click : SystemMouseCursors.none,
        child: Text(
          // '$number ${_formatKey(key)}',
          _formatKey(key),
          style: getTextStyle(isExpanded: isExpanded),
        ),
      ),
      onExpansionChanged: (value) {
        if (value) {
          _expandedKeys.add(keyString);
        } else {
          _expandedKeys.remove(keyString);
        }

        setState(() {});
      },
      tilePadding: EdgeInsets.only(left: 10, top: 5, bottom: 5),
      children: [
        Padding(
          padding: EdgeInsets.only(
            left: _getIndentationForLevel(number) + 5,
          ),
          child: _buildContent(value, number),
        ),
      ],
    );
  }

  Widget _buildMetadataGrid(Map<String, dynamic> map) {
    return LayoutBuilder(
      builder: (context, constraints) {
        int columns = constraints.maxWidth > 550 ? 3 : 2;
        double spacing = 10.0;

        List<Widget> tiles = map.entries.map((entry) {
          return Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text("${_formatKey(entry.key)}:",
                  style: getTextStyle(isExpanded: true)),
              const SizedBox(width: 2),
              Text(entry.value.toString(),
                  style: getTextStyle(isExpanded: false)),
            ],
          );
        }).toList();

        return GridView.count(
          crossAxisCount: columns,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          mainAxisSpacing: spacing,
          crossAxisSpacing: spacing,
          childAspectRatio: 5,
          children: tiles,
        );
      },
    );
  }

  Widget _buildListContent(List<dynamic> list, String parentNumber) {
    List<Widget> children = [];

    for (int i = 0; i < list.length; i++) {
      dynamic item = list[i];

      if (item is Map<String, dynamic>) {
        String title = _getListItemTitle(item, i);
        String currentNumber = '$parentNumber.${i + 1}';
        children.add(_buildSubExpansionTile(title, item, currentNumber));
      } else {
        children.add(_buildBulletPoint(item.toString()));
      }

      if (i < list.length - 1) {
        children.add(const SizedBox(height: 2));
      }
    }

    // If all items are simple values, wrap in ExpansionTileGroup
    bool hasComplexItems = list.any((item) => item is Map<String, dynamic>);

    if (hasComplexItems) {
      List<ExpansionTileCard> expansionItems = [];
      List<Widget> simpleItems = [];

      for (int i = 0; i < list.length; i++) {
        dynamic item = list[i];

        if (item is Map<String, dynamic>) {
          // Add any accumulated simple items first
          if (simpleItems.isNotEmpty) {
            expansionItems.add(_buildSimpleItemsCard(
                simpleItems, '$parentNumber.${expansionItems.length + 1}'));
            simpleItems.clear();
          }

          String title = _getListItemTitle(item, i);
          String currentNumber = '$parentNumber.${expansionItems.length + 1}';
          expansionItems
              .add(_buildSubExpansionTile(title, item, currentNumber));
        } else {
          simpleItems.add(_buildBulletPoint(item.toString()));
        }
      }

      // Add any remaining simple items
      if (simpleItems.isNotEmpty) {
        expansionItems.add(_buildSimpleItemsCard(
            simpleItems, '$parentNumber.${expansionItems.length + 1}'));
      }

      return ExpansionTileGroup(
        toggleType: ToggleType.expandOnlyCurrent,
        spaceBetweenItem: 10,
        children: expansionItems,
      );
    } else {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: children,
      );
    }
  }

  ExpansionTileCard _buildSimpleItemsCard(List<Widget> items, String number) {
    return ExpansionTileCard(
      key: Key('simple-$number'),
      childrenPadding: EdgeInsets.zero,
      elevation: 0,
      title: Text(
        '$number Items',
        style: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: Colors.black87,
        ),
      ),
      children: [
        Padding(
          padding: EdgeInsets.only(
            left: _getIndentationForLevel(number) + 5.0,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: items,
          ),
        ),
      ],
    );
  }

  bool _hasExpandableContent(dynamic value) {
    if (value is Map<String, dynamic>) {
      return value.isNotEmpty;
    } else if (value is List) {
      return value.isNotEmpty;
    }
    return false;
  }

  double _getIndentationForLevel(String number) {
    int level = number.split('.').length - 1;
    return level * 5.0;
  }

  String _getListItemTitle(Map<String, dynamic> item, int index) {
    if (item.containsKey('name')) {
      return item['name']?.toString() ?? 'Item ${index + 1}';
    }
    if (item.containsKey('title')) {
      return item['title']?.toString() ?? 'Item ${index + 1}';
    }
    if (item.containsKey('role')) {
      return item['role']?.toString() ?? 'Item ${index + 1}';
    }
    return 'Item ${index + 1}';
  }

  Widget _buildBulletPoint(String text) {
    // Check if text already contains bullet points
    bool hasBullet = text.trim().startsWith('•') ||
        text.trim().startsWith('-') ||
        text.trim().startsWith('*') ||
        RegExp(r'^\d+\.').hasMatch(text.trim());

    return Padding(
      padding: const EdgeInsets.only(bottom: 2.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!hasBullet) ...[
            Text(
              '• ',
              style: getTextStyle(),
            ),
          ],
          Expanded(
            child: Text(
              hasBullet ? text : text,
              style: getTextStyle(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextContent(String text) {
    return Text(text, style: getTextStyle());
  }

  TextStyle getTextStyle({isExpanded = false}) {
    return FontManager.getCustomStyle(
      fontSize: FontManager.s12,
      fontWeight: isExpanded ? FontWeight.bold : FontWeight.normal,
      color: AppColors.black,
      fontFamily: FontManager.fontFamilyTiemposText,
    );
  }
}

Map<String, dynamic> _getExpandData() {
  return {
    "complete_json": {
      "solution_metadata": {
        "solution_archetype": "standard",
        "complexity_level": 5,
        "domain": "digital_agency",
        "user_sophistication": "intermediate",
        "digital_density_target": 0.7,
        "entity_interconnectedness": "medium"
      },
      "foundation": {
        "business_purpose": {
          "primary_objective":
              "Implement a comprehensive CRM system to streamline client management and enhance campaign analytics for a digital marketing agency",
          "business_goals": [
            "Improve client relationship management efficiency",
            "Enhance campaign performance tracking and reporting",
            "Streamline client onboarding and project management",
            "Increase client retention through better service delivery",
            "Optimize revenue through data-driven decision making"
          ],
          "success_metrics": [
            "Client retention rate",
            "Average client lifetime value",
            "Campaign ROI metrics",
            "Project delivery time reduction",
            "Client satisfaction scores",
            "Monthly recurring revenue growth"
          ],
          "roi_expectations":
              "Expected 25-30% improvement in operational efficiency and 20% increase in client retention within first year of implementation",
          "value_proposition":
              "Centralized client management platform with advanced analytics capabilities to drive agency growth and improve service delivery"
        },
        "project_scope": {
          "included_features": [
            "Client portfolio management",
            "Campaign performance analytics",
            "Automated reporting systems",
            "Project tracking and management",
            "Client communication portal",
            "Lead management system",
            "Billing and invoice management",
            "Performance dashboards"
          ],
          "excluded_features": [
            "Social media management tools",
            "Content management system",
            "Website hosting services",
            "Email marketing platform"
          ],
          "entity_boundaries":
              "System will focus on client relationship management, campaign analytics, and business operations, excluding creative and production tools",
          "assumptions": [
            "Existing client data can be migrated",
            "Users have basic technical proficiency",
            "Internet connectivity is reliable",
            "Integration with existing tools is possible"
          ],
          "constraints": [
            "Budget limitations",
            "Implementation timeline",
            "Data security requirements",
            "Integration with existing systems"
          ]
        },
        "stakeholders": {
          "primary_users": [
            "Digital Account Manager",
            "Analytics Specialist",
            "Project Coordinator",
            "Campaign Manager"
          ],
          "secondary_users": [
            "Creative Team",
            "Finance Department",
            "Support Staff"
          ],
          "decision_makers": [
            "Client Services Director",
            "Business Development Manager"
          ],
          "business_sponsors": ["Agency Owner", "Operations Director"]
        },
        "business_context": {
          "industry_domain": "Digital Marketing Agency",
          "regulatory_environment": [
            "GDPR Data Protection Requirements",
            "CAN-SPAM Act Compliance",
            "CCPA (California Consumer Privacy Act)",
            "Digital Advertising Standards"
          ],
          "business_drivers": [
            "Increasing client demands for performance transparency",
            "Need for improved operational efficiency",
            "Competition in digital agency space",
            "Growing data analysis requirements"
          ],
          "operational_constraints": [
            "Resource availability",
            "Training requirements",
            "System downtime limitations",
            "Data migration complexity"
          ],
          "strategic_alignment":
              "Aligns with agency's goals of providing data-driven marketing services and maintaining long-term client relationships through improved service delivery and transparency"
        }
      },
      "organization": {
        "organization_structure": {
          "executive_level": {
            "Client Services Director": {
              "role": "Client Services Director",
              "department": "Client Services",
              "authority_level": "executive",
              "direct_reports": [
                "Digital Account Manager",
                "Campaign Manager",
                "Business Development Manager"
              ],
              "permissions": [
                "full system access",
                "user management",
                "contract management",
                "financial oversight"
              ]
            }
          },
          "departments": {
            "Client Services": {
              "head": "Client Services Director",
              "roles": [
                {
                  "role": "Digital Account Manager",
                  "authority_level": "senior",
                  "reports_to": "Client Services Director",
                  "permissions": [
                    "full client data access",
                    "campaign modification",
                    "reporting tools",
                    "billing management"
                  ]
                }
              ]
            },
            "Analytics": {
              "roles": [
                {
                  "role": "Analytics Specialist",
                  "authority_level": "senior",
                  "reports_to": "Analytics Manager",
                  "permissions": [
                    "analytics tools access",
                    "data export",
                    "report creation",
                    "dashboard management"
                  ]
                }
              ]
            },
            "Operations": {
              "roles": [
                {
                  "role": "Project Coordinator",
                  "authority_level": "junior",
                  "reports_to": "Operations Manager",
                  "permissions": [
                    "project management tools",
                    "basic client data access",
                    "task assignment",
                    "timeline management"
                  ]
                }
              ]
            },
            "Digital Marketing": {
              "roles": [
                {
                  "role": "Campaign Manager",
                  "authority_level": "manager",
                  "reports_to": "Client Services Director",
                  "permissions": [
                    "campaign tools access",
                    "budget management",
                    "performance tracking",
                    "strategy modification"
                  ]
                }
              ]
            },
            "Sales": {
              "roles": [
                {
                  "role": "Business Development Manager",
                  "authority_level": "manager",
                  "reports_to": "Client Services Director",
                  "permissions": [
                    "lead management",
                    "proposal tools",
                    "pricing configuration",
                    "prospect data access"
                  ]
                }
              ]
            }
          },
          "authority_levels": {
            "executive": ["Client Services Director"],
            "senior": ["Digital Account Manager", "Analytics Specialist"],
            "manager": ["Campaign Manager", "Business Development Manager"],
            "junior": ["Project Coordinator"]
          },
          "common_permissions": {
            "entity_access": ["User"],
            "function_execution": [
              "CreateClientCampaign",
              "GenerateClientAnalytics",
              "ManageClientRetainer",
              "TrackProjectDeliverables",
              "ScheduleClientMeeting"
            ],
            "workflow_initiation": [
              "New Client Onboarding & Campaign Setup",
              "Monthly Performance Review Cycle",
              "Client Retention Management"
            ]
          }
        }
      },
      "entities": {
        "core_business_entities": {
          "entity_definitions": [
            {
              "name": "User",
              "description": "System users who perform functions",
              "attributes": [
                {
                  "name": "id",
                  "type": "string",
                  "required": true,
                  "description": "Unique identifier"
                },
                {
                  "name": "name",
                  "type": "string",
                  "required": true,
                  "description": "User name"
                },
                {
                  "name": "email",
                  "type": "string",
                  "required": true,
                  "description": "Email address"
                },
                {
                  "name": "status",
                  "type": "string",
                  "required": true,
                  "description": "User status"
                },
                {
                  "name": "created_date",
                  "type": "datetime",
                  "required": true,
                  "description": "Creation date"
                }
              ],
              "relationships": ["Role", "Department"],
              "business_rules": ["Email must be unique"],
              "lifecycle_states": ["active", "inactive"],
              "domain_specific": true,
              "auto_generated": true,
              "source": "fallback_generation"
            }
          ]
        },
        "transaction_entities": {"transaction_types": []},
        "reference_entities": {
          "lookup_tables": [
            {
              "name": "CampaignStatus",
              "description":
                  "Status options for marketing campaigns and client projects",
              "data_type": "lookup",
              "refresh_pattern": "static",
              "data_source": "internal",
              "sample_values": [
                "Draft",
                "Active",
                "Paused",
                "Completed",
                "Archived"
              ]
            },
            {
              "name": "MarketingChannel",
              "description":
                  "Types of digital marketing channels available for campaigns",
              "data_type": "master_data",
              "refresh_pattern": "weekly",
              "data_source": "internal",
              "sample_values": [
                "Social Media",
                "SEO",
                "PPC",
                "Email",
                "Content Marketing",
                "Display Advertising"
              ]
            },
            {
              "name": "ClientIndustry",
              "description": "Industry categories for client classification",
              "data_type": "master_data",
              "refresh_pattern": "monthly",
              "data_source": "manual",
              "sample_values": [
                "Technology",
                "Healthcare",
                "Retail",
                "Finance",
                "Education",
                "Manufacturing"
              ]
            },
            {
              "name": "AnalyticsMetric",
              "description":
                  "Standard metrics used for campaign performance measurement",
              "data_type": "lookup",
              "refresh_pattern": "static",
              "data_source": "internal",
              "sample_values": [
                "ROI",
                "Conversion Rate",
                "CTR",
                "Engagement Rate",
                "Bounce Rate"
              ]
            },
            {
              "name": "ServicePackage",
              "description": "Predefined service packages offered to clients",
              "data_type": "master_data",
              "refresh_pattern": "quarterly",
              "data_source": "internal",
              "sample_values": ["Basic", "Professional", "Enterprise", "Custom"]
            },
            {
              "name": "SocialPlatform",
              "description": "Social media platforms integrated with the CRM",
              "data_type": "external_reference",
              "refresh_pattern": "real_time",
              "data_source": "external_api",
              "sample_values": [
                "Facebook",
                "Instagram",
                "LinkedIn",
                "Twitter",
                "TikTok"
              ]
            },
            {
              "name": "ClientTier",
              "description":
                  "Client classification based on engagement level and revenue",
              "data_type": "lookup",
              "refresh_pattern": "monthly",
              "data_source": "internal",
              "sample_values": ["Bronze", "Silver", "Gold", "Platinum"]
            },
            {
              "name": "ReportingFrequency",
              "description": "Available options for client reporting schedules",
              "data_type": "lookup",
              "refresh_pattern": "static",
              "data_source": "internal",
              "sample_values": ["Weekly", "Bi-weekly", "Monthly", "Quarterly"]
            }
          ]
        },
        "aggregate_entities": {
          "aggregation_definitions": [
            {
              "name": "ClientPortfolioPerformance",
              "description":
                  "Overall performance metrics for client accounts including campaign ROI and engagement",
              "source_entities": ["User", "Campaign", "ClientAccount"],
              "calculation_rules": [
                "AVG(campaign.roi)",
                "SUM(campaign.spend)",
                "COUNT(campaign.engagement)"
              ],
              "refresh_schedule": "daily",
              "drill_down_paths": [
                "client.campaigns.metrics",
                "client.engagement.history"
              ],
              "alert_thresholds": ["roi < 1.5", "engagement_rate < 0.02"]
            },
            {
              "name": "RevenueAnalytics",
              "description": "Revenue metrics across all clients and services",
              "source_entities": ["User", "Invoice", "Service"],
              "calculation_rules": [
                "SUM(invoice.amount)",
                "AVG(service.margin)"
              ],
              "refresh_schedule": "daily",
              "drill_down_paths": ["revenue.by.client", "revenue.by.service"],
              "alert_thresholds": [
                "monthly_revenue < target_revenue",
                "margin < 0.2"
              ]
            },
            {
              "name": "ClientHealthScore",
              "description":
                  "Composite score of client satisfaction and engagement",
              "source_entities": [
                "User",
                "ClientFeedback",
                "ClientInteraction"
              ],
              "calculation_rules": [
                "AVG(feedback.score)",
                "COUNT(interaction.frequency)"
              ],
              "refresh_schedule": "weekly",
              "drill_down_paths": [
                "client.feedback.history",
                "client.interaction.log"
              ],
              "alert_thresholds": [
                "health_score < 7.0",
                "interaction_gap > 14_days"
              ]
            },
            {
              "name": "CampaignEffectiveness",
              "description": "Aggregated campaign performance metrics",
              "source_entities": ["User", "Campaign", "CampaignMetrics"],
              "calculation_rules": [
                "AVG(campaign.conversion_rate)",
                "SUM(campaign.leads)"
              ],
              "refresh_schedule": "hourly",
              "drill_down_paths": [
                "campaign.performance.detail",
                "campaign.conversion.funnel"
              ],
              "alert_thresholds": [
                "conversion_rate < 0.01",
                "lead_cost > target_cpl"
              ]
            },
            {
              "name": "TeamProductivity",
              "description": "Team performance and productivity metrics",
              "source_entities": ["User", "Task", "TimeEntry"],
              "calculation_rules": [
                "SUM(time_entry.hours)",
                "AVG(task.completion_rate)"
              ],
              "refresh_schedule": "daily",
              "drill_down_paths": [
                "productivity.by.team",
                "productivity.by.project"
              ],
              "alert_thresholds": ["utilization < 0.75", "overdue_tasks > 5"]
            },
            {
              "name": "ServiceProfitability",
              "description": "Profitability analysis by service type",
              "source_entities": ["User", "Service", "Cost"],
              "calculation_rules": [
                "SUM(service.revenue)",
                "AVG(service.cost)"
              ],
              "refresh_schedule": "weekly",
              "drill_down_paths": [
                "profitability.by.service",
                "cost.breakdown"
              ],
              "alert_thresholds": ["profit_margin < 0.3", "cost_increase > 0.1"]
            },
            {
              "name": "ClientRetentionMetrics",
              "description": "Client retention and churn analysis",
              "source_entities": ["User", "ClientAccount", "ContractRenewal"],
              "calculation_rules": [
                "AVG(client.lifetime_value)",
                "COUNT(contract.renewals)"
              ],
              "refresh_schedule": "weekly",
              "drill_down_paths": ["retention.by.segment", "churn.analysis"],
              "alert_thresholds": ["churn_rate > 0.05", "renewal_rate < 0.8"]
            },
            {
              "name": "LeadFunnelAnalytics",
              "description": "Lead generation and conversion funnel metrics",
              "source_entities": ["User", "Lead", "Conversion"],
              "calculation_rules": [
                "COUNT(lead.stage)",
                "AVG(conversion.rate)"
              ],
              "refresh_schedule": "real_time",
              "drill_down_paths": ["funnel.stages", "conversion.journey"],
              "alert_thresholds": [
                "lead_quality < 0.6",
                "conversion_drop > 0.2"
              ]
            },
            {
              "name": "ResourceUtilization",
              "description": "Resource allocation and utilization metrics",
              "source_entities": ["User", "Resource", "Project"],
              "calculation_rules": [
                "AVG(resource.utilization)",
                "SUM(resource.capacity)"
              ],
              "refresh_schedule": "daily",
              "drill_down_paths": [
                "utilization.by.resource",
                "capacity.planning"
              ],
              "alert_thresholds": [
                "utilization > 0.9",
                "capacity_available < 0.1"
              ]
            }
          ]
        },
        "contextual_entities": {"context_triggers": {}},
        "entity_network_mapping": {"relationship_matrix": {}}
      },
      "business_rules": {
        "entity_validation_rules": {"state_validity_conditions": []},
        "state_transition_rules": {"allowed_transitions": {}},
        "business_logic_rules": {"calculation_formulas": {}}
      },
      "functions": {
        "function_definitions": {
          "function_catalog": [
            {
              "name": "CreateClientCampaign",
              "description":
                  "Create and configure a new digital marketing campaign for a client with defined objectives, channels, and budget allocation",
              "inputs": [
                {
                  "entity": "User",
                  "attributes": [
                    "client_id",
                    "campaign_type",
                    "budget",
                    "timeline",
                    "target_audience",
                    "channels"
                  ]
                }
              ],
              "outputs": [
                {
                  "entity": "User",
                  "attributes": [
                    "campaign_id",
                    "campaign_status",
                    "assigned_team"
                  ]
                }
              ],
              "business_rules": [
                "Campaign budget must be allocated across selected channels",
                "Timeline must include key milestones and deliverables",
                "Each campaign must have defined KPIs"
              ],
              "validation_criteria": [
                "Budget must be greater than minimum campaign threshold",
                "Timeline must not exceed 12 months",
                "At least one marketing channel must be selected"
              ],
              "assigned_roles": ["Campaign Manager", "Digital Account Manager"],
              "complexity_level": "high",
              "domain_specific": true,
              "auto_generated": true,
              "source": "llm_generation"
            },
            {
              "name": "GenerateClientAnalytics",
              "description":
                  "Generate comprehensive analytics report for client campaigns including ROI, engagement metrics, and conversion data",
              "inputs": [
                {
                  "entity": "User",
                  "attributes": [
                    "client_id",
                    "date_range",
                    "metrics_requested",
                    "campaign_ids"
                  ]
                }
              ],
              "outputs": [
                {
                  "entity": "User",
                  "attributes": [
                    "analytics_report",
                    "performance_metrics",
                    "recommendations"
                  ]
                }
              ],
              "business_rules": [
                "Data must be aggregated across all active campaigns",
                "Compare performance against benchmarks",
                "Include trend analysis for key metrics"
              ],
              "validation_criteria": [
                "Data must be within specified date range",
                "All requested metrics must be available",
                "Minimum data points threshold must be met"
              ],
              "assigned_roles": [
                "Analytics Specialist",
                "Digital Account Manager"
              ],
              "complexity_level": "high",
              "domain_specific": true,
              "auto_generated": true,
              "source": "llm_generation"
            },
            {
              "name": "ManageClientRetainer",
              "description":
                  "Set up and manage client retainer agreements including service scope, deliverables, and billing",
              "inputs": [
                {
                  "entity": "User",
                  "attributes": [
                    "client_id",
                    "retainer_amount",
                    "service_scope",
                    "contract_duration"
                  ]
                }
              ],
              "outputs": [
                {
                  "entity": "User",
                  "attributes": [
                    "retainer_id",
                    "billing_schedule",
                    "service_agreement"
                  ]
                }
              ],
              "business_rules": [
                "Retainer must specify included services and deliverables",
                "Auto-renewal terms must be defined",
                "Resource allocation must be specified"
              ],
              "validation_criteria": [
                "Retainer amount must meet minimum agency requirements",
                "Contract duration must be at least 3 months",
                "Service scope must align with agency capabilities"
              ],
              "assigned_roles": [
                "Client Services Director",
                "Business Development Manager"
              ],
              "complexity_level": "medium",
              "domain_specific": true,
              "auto_generated": true,
              "source": "llm_generation"
            },
            {
              "name": "TrackProjectDeliverables",
              "description":
                  "Monitor and track status of client project deliverables, deadlines, and resource allocation",
              "inputs": [
                {
                  "entity": "User",
                  "attributes": [
                    "project_id",
                    "deliverables",
                    "deadlines",
                    "assigned_resources"
                  ]
                }
              ],
              "outputs": [
                {
                  "entity": "User",
                  "attributes": [
                    "project_status",
                    "completion_percentage",
                    "resource_utilization"
                  ]
                }
              ],
              "business_rules": [
                "Each deliverable must have an assigned owner",
                "Status updates required at defined intervals",
                "Resource conflicts must be flagged"
              ],
              "validation_criteria": [
                "All deliverables must have deadlines",
                "Resource allocation cannot exceed 100%",
                "Dependencies must be identified"
              ],
              "assigned_roles": [
                "Project Coordinator",
                "Digital Account Manager"
              ],
              "complexity_level": "medium",
              "domain_specific": true,
              "auto_generated": true,
              "source": "llm_generation"
            },
            {
              "name": "ScheduleClientMeeting",
              "description":
                  "Schedule and manage client meetings, presentations, and quarterly business reviews",
              "inputs": [
                {
                  "entity": "User",
                  "attributes": [
                    "client_id",
                    "meeting_type",
                    "attendees",
                    "agenda",
                    "date_time"
                  ]
                }
              ],
              "outputs": [
                {
                  "entity": "User",
                  "attributes": [
                    "meeting_id",
                    "calendar_invites",
                    "meeting_materials"
                  ]
                }
              ],
              "business_rules": [
                "Meeting type determines required attendees",
                "QBR meetings must include performance review",
                "Follow-up actions must be documented"
              ],
              "validation_criteria": [
                "Required attendees must confirm availability",
                "Meeting materials must be prepared 24h in advance",
                "Agenda must be distributed to all participants"
              ],
              "assigned_roles": [
                "Digital Account Manager",
                "Client Services Director"
              ],
              "complexity_level": "low",
              "domain_specific": true,
              "auto_generated": true,
              "source": "llm_generation"
            }
          ]
        },
        "function_stacks": {"ui_stack": {}},
        "function_dependencies": {"input_dependencies": []}
      },
      "workflows": {
        "global_objectives": {
          "workflow_definitions": [
            {
              "name": "New Client Onboarding & Campaign Setup",
              "description":
                  "End-to-end process for onboarding new clients and initializing their first marketing campaign",
              "trigger_conditions": [
                "New client contract signed",
                "Initial payment received"
              ],
              "pathways": [
                {
                  "name": "Standard Onboarding",
                  "steps": [
                    "Schedule kickoff meeting",
                    "Document client requirements",
                    "Setup client retainer agreement",
                    "Create initial campaign structure",
                    "Set analytics tracking parameters"
                  ],
                  "functions_used": [
                    "ScheduleClientMeeting",
                    "CreateClientCampaign",
                    "ManageClientRetainer",
                    "GenerateClientAnalytics"
                  ]
                },
                {
                  "name": "Fast-track Setup",
                  "steps": [
                    "Quick brief meeting",
                    "Template-based campaign setup",
                    "Basic analytics setup"
                  ],
                  "functions_used": [
                    "ScheduleClientMeeting",
                    "CreateClientCampaign"
                  ]
                }
              ],
              "success_criteria": [
                "Campaign structure approved",
                "Analytics tracking confirmed",
                "Retainer agreement signed"
              ],
              "dependencies": [
                "CRM user access",
                "Analytics tools integration"
              ],
              "roles_involved": [
                "Account Manager",
                "Campaign Specialist",
                "Analytics Team"
              ],
              "complexity_level": "high",
              "domain_specific": true,
              "auto_generated": true,
              "source": "llm_generation"
            },
            {
              "name": "Monthly Performance Review Cycle",
              "description":
                  "Regular process for reviewing client campaign performance and adjusting strategies",
              "trigger_conditions": [
                "Month end reached",
                "Performance review scheduled"
              ],
              "pathways": [
                {
                  "name": "Standard Review",
                  "steps": [
                    "Generate performance reports",
                    "Analyze campaign metrics",
                    "Schedule review meeting",
                    "Update deliverables tracking",
                    "Adjust campaign parameters"
                  ],
                  "functions_used": [
                    "GenerateClientAnalytics",
                    "ScheduleClientMeeting",
                    "TrackProjectDeliverables",
                    "CreateClientCampaign"
                  ]
                }
              ],
              "success_criteria": [
                "Review meeting completed",
                "Performance report delivered",
                "Action items documented"
              ],
              "dependencies": [
                "Previous month's data",
                "Active campaign status"
              ],
              "roles_involved": [
                "Account Manager",
                "Analytics Specialist",
                "Client"
              ],
              "complexity_level": "medium",
              "domain_specific": true,
              "auto_generated": true,
              "source": "llm_generation"
            },
            {
              "name": "Client Retention Management",
              "description":
                  "Proactive process for maintaining client relationships and ensuring contract renewals",
              "trigger_conditions": [
                "Contract renewal approaching",
                "Client satisfaction alert"
              ],
              "pathways": [
                {
                  "name": "Retention Planning",
                  "steps": [
                    "Review client history",
                    "Analyze ROI metrics",
                    "Schedule strategy meeting",
                    "Update retainer terms",
                    "Present renewal proposal"
                  ],
                  "functions_used": [
                    "ManageClientRetainer",
                    "GenerateClientAnalytics",
                    "ScheduleClientMeeting"
                  ]
                },
                {
                  "name": "At-Risk Client Management",
                  "steps": [
                    "Analyze pain points",
                    "Emergency review meeting",
                    "Develop improvement plan",
                    "Adjust deliverables"
                  ],
                  "functions_used": [
                    "TrackProjectDeliverables",
                    "ScheduleClientMeeting",
                    "ManageClientRetainer"
                  ]
                }
              ],
              "success_criteria": [
                "Contract renewed",
                "Client satisfaction improved",
                "Updated terms agreed"
              ],
              "dependencies": [
                "Historical performance data",
                "Client feedback"
              ],
              "roles_involved": [
                "Account Director",
                "Account Manager",
                "Finance Team"
              ],
              "complexity_level": "high",
              "domain_specific": true,
              "auto_generated": true,
              "source": "llm_generation"
            }
          ]
        },
        "pathway_architecture": {"sequential_pathways": []},
        "state_management": {"workflow_states": {}}
      },
      "integrations": {
        "integrations": {
          "essential_integrations": {
            "marketing_tools": {
              "email_marketing": ["Mailchimp", "Campaign Monitor", "SendGrid"],
              "social_media": ["Hootsuite", "Buffer", "Sprout Social"],
              "analytics": ["Google Analytics", "Adobe Analytics", "Mixpanel"]
            },
            "project_management": ["Asana", "Trello", "Monday.com"],
            "communication": ["Slack", "Microsoft Teams", "Zoom"]
          },
          "recommended_integrations": {
            "advertising_platforms": [
              "Google Ads",
              "Facebook Ads Manager",
              "LinkedIn Ads"
            ],
            "content_management": ["WordPress", "HubSpot CMS", "Contentful"],
            "seo_tools": ["SEMrush", "Ahrefs", "Moz Pro"],
            "design_tools": ["Adobe Creative Cloud", "Figma", "Canva"]
          },
          "optional_integrations": {
            "reporting_tools": ["Databox", "Supermetrics", "DashThis"],
            "billing_and_invoicing": ["QuickBooks", "Xero", "FreshBooks"],
            "customer_support": ["Zendesk", "Intercom", "Help Scout"]
          },
          "api_integrations": {
            "data_enrichment": ["Clearbit", "ZoomInfo", "Hunter.io"],
            "automation": ["Zapier", "Make (Integromat)", "Workato"]
          }
        }
      },
      "intelligence": {
        "analytics_requirements": {"real_time_analytics": {}},
        "optimization_targets": {"efficiency_metrics": []}
      },
      "_validation": {
        "is_valid": true,
        "compliance_score": 0.7777777777777778,
        "missing_sections": [],
        "incomplete_sections": [],
        "validation_errors": [],
        "section_scores": {
          "solution_metadata": 1.0,
          "foundation": 1.0,
          "organization": 0.0,
          "entities": 1.0,
          "business_rules": 1.0,
          "functions": 1.0,
          "workflows": 1.0,
          "integrations": 0.0,
          "intelligence": 1.0
        },
        "validated_at": "2025-07-09T11:15:00.479218",
        "template_version": "enhanced_v3"
      }
    }
  };
}
